#!/bin/bash

# GameFlex AWS Services Initialization Script (Shell)
# This script initializes AWS services in LocalStack
# Runs automatically when LocalStack starts

set -e

echo "[AWS-INIT] Initializing AWS services in LocalStack..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[AWS-INIT]${NC} $1"
}

# Test AWS CLI and LocalStack connectivity
test_aws_connection() {
    if awslocal sts get-caller-identity > /dev/null 2>&1; then
        print_status "AWS CLI connection to LocalStack successful"
        return 0
    else
        print_error "Failed to connect to LocalStack"
        return 1
    fi
}

# These functions are now handled by individual initialization scripts
# - 00-init-cognito-users.sh: Creates Cognito users and user pool
# - 01-create-dynamodb-tables.sh: Creates DynamoDB tables with proper indexes
# - 02-seed-dynamodb-data.sh: Seeds DynamoDB with test data
# - 03-create-s3-buckets.sh: Creates S3 buckets
# - 04-deploy-lambda-functions.sh: Builds and deploys Lambda functions

# Run initialization scripts in order
run_initialization_scripts() {
    print_status "Running initialization scripts..."

    local script_dir="/etc/localstack/init/ready.d"
    local scripts=(
        "00-init-cognito-users.sh"
        "01-create-dynamodb-tables.sh"
        "02-seed-dynamodb-data.sh"
        "03-create-s3-buckets.sh"
        "04-deploy-lambda-functions.sh"
    )

    for script in "${scripts[@]}"; do
        local script_path="$script_dir/$script"
        if [ -f "$script_path" ]; then
            print_status "Running $script..."
            chmod +x "$script_path"
            if bash "$script_path"; then
                print_status "✓ $script completed successfully"
            else
                print_error "✗ $script failed"
                return 1
            fi
        else
            print_warning "Script not found: $script_path"
        fi
    done

    print_status "All initialization scripts completed successfully!"
}

# Main execution
main() {
    print_header "Initializing AWS services in LocalStack..."
    echo

    if ! test_aws_connection; then
        print_error "Cannot connect to LocalStack. Make sure it's running."
        exit 1
    fi

    # Run all initialization scripts in order
    if run_initialization_scripts; then
        echo
        print_status "AWS Services initialized successfully!"
        echo

        # Get final Cognito configuration
        local user_pool_id=$(awslocal cognito-idp list-user-pools --max-results 50 \
            --query 'UserPools[?contains(Name, `GameFlex`) || contains(Name, `gameflex`)].Id' \
            --output text | head -n1)

        local client_id=""
        if [ -n "$user_pool_id" ] && [ "$user_pool_id" != "None" ]; then
            client_id=$(awslocal cognito-idp list-user-pool-clients --user-pool-id "$user_pool_id" \
                --query 'UserPoolClients[0].ClientId' \
                --output text 2>/dev/null || echo "")
        fi

        print_status "Final Configuration:"
        echo -e "  User Pool ID: ${CYAN}$user_pool_id${NC}"
        echo -e "  Client ID: ${CYAN}$client_id${NC}"
        echo
        print_status "Initialization completed!"
    else
        print_error "Initialization failed!"
        exit 1
    fi
}

# Run main function
main "$@"
