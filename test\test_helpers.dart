import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

/// Test helpers for mocking plugins and setting up test environment
class TestHelpers {
  /// Mock SharedPreferences plugin to prevent MissingPluginException
  static void mockSharedPreferences() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/shared_preferences'),
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'getAll':
            return <String, dynamic>{};
          case 'getBool':
          case 'getInt':
          case 'getDouble':
          case 'getString':
          case 'getStringList':
            return null;
          case 'setBool':
          case 'setInt':
          case 'setDouble':
          case 'setString':
          case 'setStringList':
          case 'remove':
          case 'clear':
            return true;
          default:
            return null;
        }
      },
    );
  }

  /// Mock HTTP client to return 400 status for all requests (test environment)
  static void mockHttpClient() {
    // This is automatically handled by TestWidgetsFlutterBinding
    // All HTTP requests will return status code 400 in test environment
  }

  /// Setup test environment with all necessary mocks
  static void setupTestEnvironment() {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockSharedPreferences();
  }

  /// Clear all mocks (useful for tearDown)
  static void clearMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/shared_preferences'),
      null,
    );
  }
}
