import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'config_service.dart';

/// Base API service for communicating with AWS backend
class ApiService {
  static ApiService? _instance;
  static ApiService get instance => _instance ??= ApiService._();

  ApiService._();

  String? _cachedApiGatewayId;

  /// Get the current server URL from ConfigService
  Future<String> getServerUrl() async {
    return await ConfigService.instance.getServerUrl();
  }

  /// Get the API Gateway ID for the current environment
  Future<String> _getApiGatewayId() async {
    if (_cachedApiGatewayId != null) {
      return _cachedApiGatewayId!;
    }

    try {
      final serverUrl = await getServerUrl();

      // For LocalStack development, try to discover the API Gateway
      if (serverUrl.contains('localhost') || serverUrl.contains('127.0.0.1')) {
        try {
          // Try to get the API Gateway list from LocalStack
          final response = await http.get(
            Uri.parse('$serverUrl/_aws/apigateway/restapis'),
            headers: {'Content-Type': 'application/json'},
          );

          if (response.statusCode == 200) {
            final data = json.decode(response.body);
            final items = data['items'] as List?;

            if (items != null && items.isNotEmpty) {
              // Look for gameflex-api-development
              for (final item in items) {
                final name = item['name'] as String?;
                if (name != null && name.contains('gameflex-api')) {
                  _cachedApiGatewayId = item['id'] as String;
                  developer.log(
                    'Found LocalStack API Gateway: $_cachedApiGatewayId',
                  );
                  return _cachedApiGatewayId!;
                }
              }

              // Fallback to first API
              _cachedApiGatewayId = items.first['id'] as String;
              developer.log(
                'Using first available API Gateway: $_cachedApiGatewayId',
              );
              return _cachedApiGatewayId!;
            }
          }
        } catch (e) {
          developer.log('Failed to discover API Gateway, using fallback: $e');
        }

        // Fallback to CloudFormation-created API Gateway
        _cachedApiGatewayId = 'rwwawvaaau';
        developer.log(
          'Using fallback LocalStack API Gateway ID: $_cachedApiGatewayId',
        );
        return _cachedApiGatewayId!;
      }

      // For production/staging, try to discover the API Gateway
      final response = await http.get(
        Uri.parse('$serverUrl/restapis'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final items = data['items'] as List?;

        if (items != null && items.isNotEmpty) {
          // Look for gameflex-api-development or similar
          for (final item in items) {
            final name = item['name'] as String?;
            if (name != null && name.contains('gameflex-api')) {
              _cachedApiGatewayId = item['id'] as String;
              return _cachedApiGatewayId!;
            }
          }

          // Fallback to first API if no gameflex-api found
          _cachedApiGatewayId = items.first['id'] as String;
          return _cachedApiGatewayId!;
        }
      }

      throw Exception('No API Gateway found');
    } catch (e) {
      developer.log('Failed to get API Gateway ID: $e');
      throw Exception('Failed to connect to API Gateway: $e');
    }
  }

  /// Make an HTTP request to the AWS API Gateway
  Future<http.Response> makeRequest({
    required String method,
    required String path,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    String? accessToken,
  }) async {
    try {
      final serverUrl = await getServerUrl();
      final apiGatewayId = await _getApiGatewayId();
      final stage = 'development'; // TODO: Make this environment-aware

      // Ensure path starts with /
      final cleanPath = path.startsWith('/') ? path : '/$path';
      final url =
          '$serverUrl/restapis/$apiGatewayId/$stage/_user_request_$cleanPath';
      final uri = Uri.parse(url);

      final requestHeaders = <String, String>{
        'Content-Type': 'application/json',
        ...?headers,
      };

      if (accessToken != null) {
        requestHeaders['Authorization'] = 'Bearer $accessToken';
      }

      developer.log('API Request: $method $url');
      print('🔍 DEBUG: API Request URL: $url');
      print('🔍 DEBUG: Request Headers: $requestHeaders');

      http.Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(uri, headers: requestHeaders);
          break;
        case 'POST':
          response = await http.post(
            uri,
            headers: requestHeaders,
            body: body != null ? json.encode(body) : null,
          );
          break;
        case 'PUT':
          response = await http.put(
            uri,
            headers: requestHeaders,
            body: body != null ? json.encode(body) : null,
          );
          break;
        case 'DELETE':
          response = await http.delete(uri, headers: requestHeaders);
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      developer.log('API Response: ${response.statusCode}');
      print('🔍 DEBUG: Response Status: ${response.statusCode}');
      print('🔍 DEBUG: Response Body: ${response.body}');

      return response;
    } catch (e) {
      developer.log('API Request failed: $e');
      rethrow;
    }
  }

  /// Parse JSON response and handle errors
  Map<String, dynamic> parseResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return json.decode(response.body) as Map<String, dynamic>;
    } else {
      final errorBody =
          response.body.isNotEmpty
              ? json.decode(response.body) as Map<String, dynamic>
              : <String, dynamic>{};

      final errorMessage =
          errorBody['error'] as String? ??
          errorBody['message'] as String? ??
          'HTTP ${response.statusCode}';

      throw ApiException(
        message: errorMessage,
        statusCode: response.statusCode,
        details: errorBody,
      );
    }
  }

  /// Clear cached values (useful for testing or environment changes)
  void clearCache() {
    _cachedApiGatewayId = null;
    ConfigService.instance.clearCache();
  }
}

/// Custom exception for API errors
class ApiException implements Exception {
  final String message;
  final int statusCode;
  final Map<String, dynamic> details;

  ApiException({
    required this.message,
    required this.statusCode,
    this.details = const {},
  });

  @override
  String toString() => 'ApiException: $message (HTTP $statusCode)';
}
