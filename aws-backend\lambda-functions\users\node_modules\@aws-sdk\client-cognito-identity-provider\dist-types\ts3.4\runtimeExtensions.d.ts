import { CognitoIdentityProviderExtensionConfiguration } from "./extensionConfiguration";
export interface RuntimeExtension {
  configure(
    extensionConfiguration: CognitoIdentityProviderExtensionConfiguration
  ): void;
}
export interface RuntimeExtensionsConfig {
  extensions: RuntimeExtension[];
}
export declare const resolveRuntimeExtensions: (
  runtimeConfig: any,
  extensions: RuntimeExtension[]
) => any;
