import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { CognitoIdentityProviderClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../CognitoIdentityProviderClient";
import { RevokeTokenRequest, RevokeTokenResponse } from "../models/models_1";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link RevokeTokenCommand}.
 */
export interface RevokeTokenCommandInput extends RevokeTokenRequest {
}
/**
 * @public
 *
 * The output of {@link RevokeTokenCommand}.
 */
export interface RevokeTokenCommandOutput extends RevokeTokenResponse, __MetadataBearer {
}
declare const RevokeTokenCommand_base: {
    new (input: RevokeTokenCommandInput): import("@smithy/smithy-client").CommandImpl<RevokeTokenCommandInput, RevokeTokenCommandOutput, CognitoIdentityProviderClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: RevokeTokenCommandInput): import("@smithy/smithy-client").CommandImpl<RevokeTokenCommandInput, RevokeTokenCommandOutput, CognitoIdentityProviderClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Revokes all of the access tokens generated by, and at the same time as, the specified
 *             refresh token. After a token is revoked, you can't use the revoked token to access Amazon Cognito
 *             user APIs, or to authorize access to your resource server.</p>
 *          <note>
 *             <p>Amazon Cognito doesn't evaluate Identity and Access Management (IAM) policies in requests for this API operation. For
 *     this operation, you can't use IAM credentials to authorize requests, and you can't
 *     grant IAM permissions in policies. For more information about authorization models in
 *     Amazon Cognito, see <a href="https://docs.aws.amazon.com/cognito/latest/developerguide/user-pools-API-operations.html">Using the Amazon Cognito user pools API and user pool endpoints</a>.</p>
 *          </note>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { CognitoIdentityProviderClient, RevokeTokenCommand } from "@aws-sdk/client-cognito-identity-provider"; // ES Modules import
 * // const { CognitoIdentityProviderClient, RevokeTokenCommand } = require("@aws-sdk/client-cognito-identity-provider"); // CommonJS import
 * const client = new CognitoIdentityProviderClient(config);
 * const input = { // RevokeTokenRequest
 *   Token: "STRING_VALUE", // required
 *   ClientId: "STRING_VALUE", // required
 *   ClientSecret: "STRING_VALUE",
 * };
 * const command = new RevokeTokenCommand(input);
 * const response = await client.send(command);
 * // {};
 *
 * ```
 *
 * @param RevokeTokenCommandInput - {@link RevokeTokenCommandInput}
 * @returns {@link RevokeTokenCommandOutput}
 * @see {@link RevokeTokenCommandInput} for command's `input` shape.
 * @see {@link RevokeTokenCommandOutput} for command's `response` shape.
 * @see {@link CognitoIdentityProviderClientResolvedConfig | config} for CognitoIdentityProviderClient's `config` shape.
 *
 * @throws {@link ForbiddenException} (client fault)
 *  <p>This exception is thrown when WAF doesn't allow your request based on a web
 *             ACL that's associated with your user pool.</p>
 *
 * @throws {@link InternalErrorException} (server fault)
 *  <p>This exception is thrown when Amazon Cognito encounters an internal error.</p>
 *
 * @throws {@link InvalidParameterException} (client fault)
 *  <p>This exception is thrown when the Amazon Cognito service encounters an invalid
 *             parameter.</p>
 *
 * @throws {@link TooManyRequestsException} (client fault)
 *  <p>This exception is thrown when the user has made too many requests for a given
 *             operation.</p>
 *
 * @throws {@link UnauthorizedException} (client fault)
 *  <p>Exception that is thrown when the request isn't authorized. This can happen due to an
 *             invalid access token in the request.</p>
 *
 * @throws {@link UnsupportedOperationException} (client fault)
 *  <p>Exception that is thrown when you attempt to perform an operation that isn't enabled
 *             for the user pool client.</p>
 *
 * @throws {@link UnsupportedTokenTypeException} (client fault)
 *  <p>Exception that is thrown when an unsupported token is passed to an operation.</p>
 *
 * @throws {@link CognitoIdentityProviderServiceException}
 * <p>Base exception class for all service exceptions from CognitoIdentityProvider service.</p>
 *
 *
 * @public
 */
export declare class RevokeTokenCommand extends RevokeTokenCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: RevokeTokenRequest;
            output: {};
        };
        sdk: {
            input: RevokeTokenCommandInput;
            output: RevokeTokenCommandOutput;
        };
    };
}
