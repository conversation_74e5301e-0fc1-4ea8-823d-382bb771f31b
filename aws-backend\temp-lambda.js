exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));
    
    const body = JSON.parse(event.body || '{}');
    const path = event.path || event.rawPath || '';
    const method = event.httpMethod || event.requestContext?.http?.method || 'GET';
    
    console.log('Path:', path, 'Method:', method);
    
    // Handle authentication endpoints
    if (path.includes('signin') && method === 'POST') {
        return {
            statusCode: 200,
            headers: { 
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({
                success: true,
                user: { 
                    id: 'test-user-id', 
                    email: body.email || '<EMAIL>',
                    name: 'Test User'
                },
                accessToken: 'test-access-token',
                refreshToken: 'test-refresh-token'
            })
        };
    }
    
    // Handle posts endpoints
    if (path.includes('posts') && method === 'GET') {
        return {
            statusCode: 200,
            headers: { 
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({
                posts: [
                    {
                        id: 'test-post-1',
                        content: 'Test post from AWS backend',
                        userId: 'test-user-id',
                        createdAt: new Date().toISOString()
                    }
                ],
                hasMore: false
            })
        };
    }
    
    if (path.includes('posts') && method === 'POST') {
        return {
            statusCode: 200,
            headers: { 
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({
                id: 'new-post-' + Date.now(),
                content: body.content || 'Default content',
                userId: 'test-user-id',
                createdAt: new Date().toISOString()
            })
        };
    }
    
    // Handle OPTIONS requests for CORS
    if (method === 'OPTIONS') {
        return {
            statusCode: 200,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: ''
        };
    }
    
    // Default response
    return {
        statusCode: 404,
        headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({ 
            error: 'Endpoint not found',
            path: path,
            method: method
        })
    };
};
