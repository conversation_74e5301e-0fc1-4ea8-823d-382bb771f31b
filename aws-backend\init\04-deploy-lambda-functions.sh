#!/bin/bash

# GameFlex Lambda Functions Deployment Script
# This script builds and deploys all Lambda functions for the GameFlex application
# Runs automatically after DynamoDB and S3 setup

set -e

echo "[LAMBDA] Deploying Lambda functions for GameFlex..."

# Function to build and deploy a Lambda function
deploy_lambda_function() {
    local function_name=$1
    local function_dir=$2
    local handler=$3
    local description=$4

    echo "[INFO] Deploying Lambda function: $function_name"

    # Check if function directory exists
    if [ ! -d "/opt/lambda-functions/$function_dir" ]; then
        echo "[WARN] Lambda function directory not found: /opt/lambda-functions/$function_dir"
        return 0
    fi

    cd "/opt/lambda-functions/$function_dir"

    # Install all dependencies (including devDependencies for building)
    echo "[INFO] Installing all dependencies for $function_name..."
    npm install --silent

    # Build TypeScript
    echo "[INFO] Building TypeScript for $function_name..."
    npm run build --silent

    # Install only production dependencies for deployment
    echo "[INFO] Installing production dependencies for $function_name..."
    npm install --production --silent

    # Create deployment package
    echo "[INFO] Creating deployment package for $function_name..."
    cd dist
    zip -r "../${function_name}.zip" . -q
    cd ..
    zip -r "${function_name}.zip" node_modules -q

    # Get User Pool information for environment variables
    local user_pool_id=$(awslocal cognito-idp list-user-pools --max-results 50 \
        --query 'UserPools[?contains(Name, `GameFlex`) || contains(Name, `gameflex`)].Id' \
        --output text 2>/dev/null | head -n1)

    local user_pool_client_id=""
    if [ -n "$user_pool_id" ] && [ "$user_pool_id" != "None" ]; then
        user_pool_client_id=$(awslocal cognito-idp list-user-pool-clients --user-pool-id "$user_pool_id" \
            --query 'UserPoolClients[0].ClientId' \
            --output text 2>/dev/null || echo "")
    fi

    # Check if function exists
    local function_exists=$(awslocal lambda get-function --function-name "$function_name" 2>/dev/null || echo "")

    if [ -n "$function_exists" ]; then
        echo "[INFO] Updating existing Lambda function: $function_name"
        
        # Update function code
        awslocal lambda update-function-code \
            --function-name "$function_name" \
            --zip-file "fileb://${function_name}.zip" > /dev/null

        # Update function configuration
        awslocal lambda update-function-configuration \
            --function-name "$function_name" \
            --handler "$handler" \
            --environment "Variables={COGNITO_USER_POOL_ID=$user_pool_id,COGNITO_USER_POOL_CLIENT_ID=$user_pool_client_id,AWS_ENDPOINT_URL=http://host.docker.internal:45660,AWS_DEFAULT_REGION=us-east-1}" > /dev/null
    else
        echo "[INFO] Creating new Lambda function: $function_name"
        
        # Create function
        awslocal lambda create-function \
            --function-name "$function_name" \
            --runtime nodejs18.x \
            --role "arn:aws:iam::000000000000:role/lambda-role" \
            --handler "$handler" \
            --zip-file "fileb://${function_name}.zip" \
            --description "$description" \
            --environment "Variables={COGNITO_USER_POOL_ID=$user_pool_id,COGNITO_USER_POOL_CLIENT_ID=$user_pool_client_id,AWS_ENDPOINT_URL=http://host.docker.internal:45660,AWS_DEFAULT_REGION=us-east-1}" > /dev/null
    fi

    # Clean up
    rm -f "${function_name}.zip"

    echo "[INFO] Successfully deployed Lambda function: $function_name"
    cd /opt/lambda-functions
}

# Set AWS environment variables for LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=us-east-1

# Wait for Lambda service to be available
echo "[INFO] Waiting for Lambda service to be available..."
timeout=60
counter=0
while [ $counter -lt $timeout ]; do
    if awslocal lambda list-functions > /dev/null 2>&1; then
        echo "[INFO] Lambda service is available"
        break
    fi
    sleep 2
    counter=$((counter + 2))
    if [ $counter -ge $timeout ]; then
        echo "[ERROR] Lambda service not available within $timeout seconds"
        echo "[INFO] Attempting to continue anyway..."
        break
    fi
done

# Ensure IAM role exists for Lambda functions
echo "[INFO] Ensuring Lambda execution role exists..."
role_exists=$(awslocal iam get-role --role-name lambda-role 2>/dev/null || echo "")
if [ -z "$role_exists" ]; then
    echo "[INFO] Creating Lambda execution role..."
    
    # Create trust policy
    cat > /tmp/lambda-trust-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "lambda.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
EOF

    awslocal iam create-role \
        --role-name lambda-role \
        --assume-role-policy-document file:///tmp/lambda-trust-policy.json > /dev/null

    awslocal iam attach-role-policy \
        --role-name lambda-role \
        --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole > /dev/null

    rm -f /tmp/lambda-trust-policy.json
    echo "[INFO] Lambda execution role created"
else
    echo "[INFO] Lambda execution role already exists"
fi

# Deploy Lambda functions
echo "[INFO] Starting Lambda function deployment..."

# Deploy auth function
deploy_lambda_function "gameflex-auth-development" "auth" "handler.handler" "GameFlex authentication functions"

# Deploy posts function
deploy_lambda_function "gameflex-posts-development" "posts" "handler.handler" "GameFlex posts management functions"

# Deploy users function
deploy_lambda_function "gameflex-users-development" "users" "handler.handler" "GameFlex user management functions"

# Deploy media function
deploy_lambda_function "gameflex-media-development" "media" "handler.handler" "GameFlex media management functions"

echo "[LAMBDA] All Lambda functions deployed successfully!"
echo "[INFO] Lambda functions are ready with proper TypeScript implementations"
