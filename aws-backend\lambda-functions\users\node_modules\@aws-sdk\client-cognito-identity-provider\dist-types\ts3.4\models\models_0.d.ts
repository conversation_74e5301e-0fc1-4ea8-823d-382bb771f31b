import { ExceptionOptionType as __ExceptionOptionType } from "@smithy/smithy-client";
import { DocumentType as __DocumentType } from "@smithy/types";
import { CognitoIdentityProviderServiceException as __BaseException } from "./CognitoIdentityProviderServiceException";
export declare const RecoveryOptionNameType: {
  readonly ADMIN_ONLY: "admin_only";
  readonly VERIFIED_EMAIL: "verified_email";
  readonly VERIFIED_PHONE_NUMBER: "verified_phone_number";
};
export type RecoveryOptionNameType =
  (typeof RecoveryOptionNameType)[keyof typeof RecoveryOptionNameType];
export interface RecoveryOptionType {
  Priority: number | undefined;
  Name: RecoveryOptionNameType | undefined;
}
export interface AccountRecoverySettingType {
  RecoveryMechanisms?: RecoveryOptionType[] | undefined;
}
export declare const AccountTakeoverEventActionType: {
  readonly BLOCK: "BLOCK";
  readonly MFA_IF_CONFIGURED: "MFA_IF_CONFIGURED";
  readonly MFA_REQUIRED: "MFA_REQUIRED";
  readonly NO_ACTION: "NO_ACTION";
};
export type AccountTakeoverEventActionType =
  (typeof AccountTakeoverEventActionType)[keyof typeof AccountTakeoverEventActionType];
export interface AccountTakeoverActionType {
  Notify: boolean | undefined;
  EventAction: AccountTakeoverEventActionType | undefined;
}
export interface AccountTakeoverActionsType {
  LowAction?: AccountTakeoverActionType | undefined;
  MediumAction?: AccountTakeoverActionType | undefined;
  HighAction?: AccountTakeoverActionType | undefined;
}
export interface NotifyEmailType {
  Subject: string | undefined;
  HtmlBody?: string | undefined;
  TextBody?: string | undefined;
}
export interface NotifyConfigurationType {
  From?: string | undefined;
  ReplyTo?: string | undefined;
  SourceArn: string | undefined;
  BlockEmail?: NotifyEmailType | undefined;
  NoActionEmail?: NotifyEmailType | undefined;
  MfaEmail?: NotifyEmailType | undefined;
}
export interface AccountTakeoverRiskConfigurationType {
  NotifyConfiguration?: NotifyConfigurationType | undefined;
  Actions: AccountTakeoverActionsType | undefined;
}
export declare const AttributeDataType: {
  readonly BOOLEAN: "Boolean";
  readonly DATETIME: "DateTime";
  readonly NUMBER: "Number";
  readonly STRING: "String";
};
export type AttributeDataType =
  (typeof AttributeDataType)[keyof typeof AttributeDataType];
export interface NumberAttributeConstraintsType {
  MinValue?: string | undefined;
  MaxValue?: string | undefined;
}
export interface StringAttributeConstraintsType {
  MinLength?: string | undefined;
  MaxLength?: string | undefined;
}
export interface SchemaAttributeType {
  Name?: string | undefined;
  AttributeDataType?: AttributeDataType | undefined;
  DeveloperOnlyAttribute?: boolean | undefined;
  Mutable?: boolean | undefined;
  Required?: boolean | undefined;
  NumberAttributeConstraints?: NumberAttributeConstraintsType | undefined;
  StringAttributeConstraints?: StringAttributeConstraintsType | undefined;
}
export interface AddCustomAttributesRequest {
  UserPoolId: string | undefined;
  CustomAttributes: SchemaAttributeType[] | undefined;
}
export interface AddCustomAttributesResponse {}
export declare class InternalErrorException extends __BaseException {
  readonly name: "InternalErrorException";
  readonly $fault: "server";
  constructor(
    opts: __ExceptionOptionType<InternalErrorException, __BaseException>
  );
}
export declare class InvalidParameterException extends __BaseException {
  readonly name: "InvalidParameterException";
  readonly $fault: "client";
  reasonCode?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<InvalidParameterException, __BaseException>
  );
}
export declare class NotAuthorizedException extends __BaseException {
  readonly name: "NotAuthorizedException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<NotAuthorizedException, __BaseException>
  );
}
export declare class ResourceNotFoundException extends __BaseException {
  readonly name: "ResourceNotFoundException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<ResourceNotFoundException, __BaseException>
  );
}
export declare class TooManyRequestsException extends __BaseException {
  readonly name: "TooManyRequestsException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<TooManyRequestsException, __BaseException>
  );
}
export declare class UserImportInProgressException extends __BaseException {
  readonly name: "UserImportInProgressException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<UserImportInProgressException, __BaseException>
  );
}
export interface AdminAddUserToGroupRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  GroupName: string | undefined;
}
export declare class UserNotFoundException extends __BaseException {
  readonly name: "UserNotFoundException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<UserNotFoundException, __BaseException>
  );
}
export interface AdminConfirmSignUpRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  ClientMetadata?: Record<string, string> | undefined;
}
export interface AdminConfirmSignUpResponse {}
export declare class InvalidLambdaResponseException extends __BaseException {
  readonly name: "InvalidLambdaResponseException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<InvalidLambdaResponseException, __BaseException>
  );
}
export declare class LimitExceededException extends __BaseException {
  readonly name: "LimitExceededException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<LimitExceededException, __BaseException>
  );
}
export declare class TooManyFailedAttemptsException extends __BaseException {
  readonly name: "TooManyFailedAttemptsException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<TooManyFailedAttemptsException, __BaseException>
  );
}
export declare class UnexpectedLambdaException extends __BaseException {
  readonly name: "UnexpectedLambdaException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<UnexpectedLambdaException, __BaseException>
  );
}
export declare class UserLambdaValidationException extends __BaseException {
  readonly name: "UserLambdaValidationException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<UserLambdaValidationException, __BaseException>
  );
}
export declare const DeliveryMediumType: {
  readonly EMAIL: "EMAIL";
  readonly SMS: "SMS";
};
export type DeliveryMediumType =
  (typeof DeliveryMediumType)[keyof typeof DeliveryMediumType];
export declare const MessageActionType: {
  readonly RESEND: "RESEND";
  readonly SUPPRESS: "SUPPRESS";
};
export type MessageActionType =
  (typeof MessageActionType)[keyof typeof MessageActionType];
export interface AttributeType {
  Name: string | undefined;
  Value?: string | undefined;
}
export interface AdminCreateUserRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  UserAttributes?: AttributeType[] | undefined;
  ValidationData?: AttributeType[] | undefined;
  TemporaryPassword?: string | undefined;
  ForceAliasCreation?: boolean | undefined;
  MessageAction?: MessageActionType | undefined;
  DesiredDeliveryMediums?: DeliveryMediumType[] | undefined;
  ClientMetadata?: Record<string, string> | undefined;
}
export interface MFAOptionType {
  DeliveryMedium?: DeliveryMediumType | undefined;
  AttributeName?: string | undefined;
}
export declare const UserStatusType: {
  readonly ARCHIVED: "ARCHIVED";
  readonly COMPROMISED: "COMPROMISED";
  readonly CONFIRMED: "CONFIRMED";
  readonly EXTERNAL_PROVIDER: "EXTERNAL_PROVIDER";
  readonly FORCE_CHANGE_PASSWORD: "FORCE_CHANGE_PASSWORD";
  readonly RESET_REQUIRED: "RESET_REQUIRED";
  readonly UNCONFIRMED: "UNCONFIRMED";
  readonly UNKNOWN: "UNKNOWN";
};
export type UserStatusType =
  (typeof UserStatusType)[keyof typeof UserStatusType];
export interface UserType {
  Username?: string | undefined;
  Attributes?: AttributeType[] | undefined;
  UserCreateDate?: Date | undefined;
  UserLastModifiedDate?: Date | undefined;
  Enabled?: boolean | undefined;
  UserStatus?: UserStatusType | undefined;
  MFAOptions?: MFAOptionType[] | undefined;
}
export interface AdminCreateUserResponse {
  User?: UserType | undefined;
}
export declare class CodeDeliveryFailureException extends __BaseException {
  readonly name: "CodeDeliveryFailureException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<CodeDeliveryFailureException, __BaseException>
  );
}
export declare class InvalidPasswordException extends __BaseException {
  readonly name: "InvalidPasswordException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<InvalidPasswordException, __BaseException>
  );
}
export declare class InvalidSmsRoleAccessPolicyException extends __BaseException {
  readonly name: "InvalidSmsRoleAccessPolicyException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      InvalidSmsRoleAccessPolicyException,
      __BaseException
    >
  );
}
export declare class InvalidSmsRoleTrustRelationshipException extends __BaseException {
  readonly name: "InvalidSmsRoleTrustRelationshipException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      InvalidSmsRoleTrustRelationshipException,
      __BaseException
    >
  );
}
export declare class PreconditionNotMetException extends __BaseException {
  readonly name: "PreconditionNotMetException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<PreconditionNotMetException, __BaseException>
  );
}
export declare class UnsupportedUserStateException extends __BaseException {
  readonly name: "UnsupportedUserStateException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<UnsupportedUserStateException, __BaseException>
  );
}
export declare class UsernameExistsException extends __BaseException {
  readonly name: "UsernameExistsException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<UsernameExistsException, __BaseException>
  );
}
export interface MessageTemplateType {
  SMSMessage?: string | undefined;
  EmailMessage?: string | undefined;
  EmailSubject?: string | undefined;
}
export interface AdminCreateUserConfigType {
  AllowAdminCreateUserOnly?: boolean | undefined;
  UnusedAccountValidityDays?: number | undefined;
  InviteMessageTemplate?: MessageTemplateType | undefined;
}
export interface AdminDeleteUserRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
}
export interface AdminDeleteUserAttributesRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  UserAttributeNames: string[] | undefined;
}
export interface AdminDeleteUserAttributesResponse {}
export interface ProviderUserIdentifierType {
  ProviderName?: string | undefined;
  ProviderAttributeName?: string | undefined;
  ProviderAttributeValue?: string | undefined;
}
export interface AdminDisableProviderForUserRequest {
  UserPoolId: string | undefined;
  User: ProviderUserIdentifierType | undefined;
}
export interface AdminDisableProviderForUserResponse {}
export declare class AliasExistsException extends __BaseException {
  readonly name: "AliasExistsException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<AliasExistsException, __BaseException>
  );
}
export interface AdminDisableUserRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
}
export interface AdminDisableUserResponse {}
export interface AdminEnableUserRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
}
export interface AdminEnableUserResponse {}
export interface AdminForgetDeviceRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  DeviceKey: string | undefined;
}
export declare class InvalidUserPoolConfigurationException extends __BaseException {
  readonly name: "InvalidUserPoolConfigurationException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      InvalidUserPoolConfigurationException,
      __BaseException
    >
  );
}
export interface AdminGetDeviceRequest {
  DeviceKey: string | undefined;
  UserPoolId: string | undefined;
  Username: string | undefined;
}
export interface DeviceType {
  DeviceKey?: string | undefined;
  DeviceAttributes?: AttributeType[] | undefined;
  DeviceCreateDate?: Date | undefined;
  DeviceLastModifiedDate?: Date | undefined;
  DeviceLastAuthenticatedDate?: Date | undefined;
}
export interface AdminGetDeviceResponse {
  Device: DeviceType | undefined;
}
export interface AdminGetUserRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
}
export interface AdminGetUserResponse {
  Username: string | undefined;
  UserAttributes?: AttributeType[] | undefined;
  UserCreateDate?: Date | undefined;
  UserLastModifiedDate?: Date | undefined;
  Enabled?: boolean | undefined;
  UserStatus?: UserStatusType | undefined;
  MFAOptions?: MFAOptionType[] | undefined;
  PreferredMfaSetting?: string | undefined;
  UserMFASettingList?: string[] | undefined;
}
export interface AnalyticsMetadataType {
  AnalyticsEndpointId?: string | undefined;
}
export declare const AuthFlowType: {
  readonly ADMIN_NO_SRP_AUTH: "ADMIN_NO_SRP_AUTH";
  readonly ADMIN_USER_PASSWORD_AUTH: "ADMIN_USER_PASSWORD_AUTH";
  readonly CUSTOM_AUTH: "CUSTOM_AUTH";
  readonly REFRESH_TOKEN: "REFRESH_TOKEN";
  readonly REFRESH_TOKEN_AUTH: "REFRESH_TOKEN_AUTH";
  readonly USER_AUTH: "USER_AUTH";
  readonly USER_PASSWORD_AUTH: "USER_PASSWORD_AUTH";
  readonly USER_SRP_AUTH: "USER_SRP_AUTH";
};
export type AuthFlowType = (typeof AuthFlowType)[keyof typeof AuthFlowType];
export interface HttpHeader {
  headerName?: string | undefined;
  headerValue?: string | undefined;
}
export interface ContextDataType {
  IpAddress: string | undefined;
  ServerName: string | undefined;
  ServerPath: string | undefined;
  HttpHeaders: HttpHeader[] | undefined;
  EncodedData?: string | undefined;
}
export interface AdminInitiateAuthRequest {
  UserPoolId: string | undefined;
  ClientId: string | undefined;
  AuthFlow: AuthFlowType | undefined;
  AuthParameters?: Record<string, string> | undefined;
  ClientMetadata?: Record<string, string> | undefined;
  AnalyticsMetadata?: AnalyticsMetadataType | undefined;
  ContextData?: ContextDataType | undefined;
  Session?: string | undefined;
}
export interface NewDeviceMetadataType {
  DeviceKey?: string | undefined;
  DeviceGroupKey?: string | undefined;
}
export interface AuthenticationResultType {
  AccessToken?: string | undefined;
  ExpiresIn?: number | undefined;
  TokenType?: string | undefined;
  RefreshToken?: string | undefined;
  IdToken?: string | undefined;
  NewDeviceMetadata?: NewDeviceMetadataType | undefined;
}
export declare const ChallengeNameType: {
  readonly ADMIN_NO_SRP_AUTH: "ADMIN_NO_SRP_AUTH";
  readonly CUSTOM_CHALLENGE: "CUSTOM_CHALLENGE";
  readonly DEVICE_PASSWORD_VERIFIER: "DEVICE_PASSWORD_VERIFIER";
  readonly DEVICE_SRP_AUTH: "DEVICE_SRP_AUTH";
  readonly EMAIL_OTP: "EMAIL_OTP";
  readonly MFA_SETUP: "MFA_SETUP";
  readonly NEW_PASSWORD_REQUIRED: "NEW_PASSWORD_REQUIRED";
  readonly PASSWORD: "PASSWORD";
  readonly PASSWORD_SRP: "PASSWORD_SRP";
  readonly PASSWORD_VERIFIER: "PASSWORD_VERIFIER";
  readonly SELECT_CHALLENGE: "SELECT_CHALLENGE";
  readonly SELECT_MFA_TYPE: "SELECT_MFA_TYPE";
  readonly SMS_MFA: "SMS_MFA";
  readonly SMS_OTP: "SMS_OTP";
  readonly SOFTWARE_TOKEN_MFA: "SOFTWARE_TOKEN_MFA";
  readonly WEB_AUTHN: "WEB_AUTHN";
};
export type ChallengeNameType =
  (typeof ChallengeNameType)[keyof typeof ChallengeNameType];
export interface AdminInitiateAuthResponse {
  ChallengeName?: ChallengeNameType | undefined;
  Session?: string | undefined;
  ChallengeParameters?: Record<string, string> | undefined;
  AuthenticationResult?: AuthenticationResultType | undefined;
  AvailableChallenges?: ChallengeNameType[] | undefined;
}
export declare class InvalidEmailRoleAccessPolicyException extends __BaseException {
  readonly name: "InvalidEmailRoleAccessPolicyException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      InvalidEmailRoleAccessPolicyException,
      __BaseException
    >
  );
}
export declare class MFAMethodNotFoundException extends __BaseException {
  readonly name: "MFAMethodNotFoundException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<MFAMethodNotFoundException, __BaseException>
  );
}
export declare class PasswordResetRequiredException extends __BaseException {
  readonly name: "PasswordResetRequiredException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<PasswordResetRequiredException, __BaseException>
  );
}
export declare class UnsupportedOperationException extends __BaseException {
  readonly name: "UnsupportedOperationException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<UnsupportedOperationException, __BaseException>
  );
}
export declare class UserNotConfirmedException extends __BaseException {
  readonly name: "UserNotConfirmedException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<UserNotConfirmedException, __BaseException>
  );
}
export interface AdminLinkProviderForUserRequest {
  UserPoolId: string | undefined;
  DestinationUser: ProviderUserIdentifierType | undefined;
  SourceUser: ProviderUserIdentifierType | undefined;
}
export interface AdminLinkProviderForUserResponse {}
export interface AdminListDevicesRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  Limit?: number | undefined;
  PaginationToken?: string | undefined;
}
export interface AdminListDevicesResponse {
  Devices?: DeviceType[] | undefined;
  PaginationToken?: string | undefined;
}
export interface AdminListGroupsForUserRequest {
  Username: string | undefined;
  UserPoolId: string | undefined;
  Limit?: number | undefined;
  NextToken?: string | undefined;
}
export interface GroupType {
  GroupName?: string | undefined;
  UserPoolId?: string | undefined;
  Description?: string | undefined;
  RoleArn?: string | undefined;
  Precedence?: number | undefined;
  LastModifiedDate?: Date | undefined;
  CreationDate?: Date | undefined;
}
export interface AdminListGroupsForUserResponse {
  Groups?: GroupType[] | undefined;
  NextToken?: string | undefined;
}
export interface AdminListUserAuthEventsRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  MaxResults?: number | undefined;
  NextToken?: string | undefined;
}
export declare const ChallengeName: {
  readonly Mfa: "Mfa";
  readonly Password: "Password";
};
export type ChallengeName = (typeof ChallengeName)[keyof typeof ChallengeName];
export declare const ChallengeResponse: {
  readonly Failure: "Failure";
  readonly Success: "Success";
};
export type ChallengeResponse =
  (typeof ChallengeResponse)[keyof typeof ChallengeResponse];
export interface ChallengeResponseType {
  ChallengeName?: ChallengeName | undefined;
  ChallengeResponse?: ChallengeResponse | undefined;
}
export interface EventContextDataType {
  IpAddress?: string | undefined;
  DeviceName?: string | undefined;
  Timezone?: string | undefined;
  City?: string | undefined;
  Country?: string | undefined;
}
export declare const FeedbackValueType: {
  readonly INVALID: "Invalid";
  readonly VALID: "Valid";
};
export type FeedbackValueType =
  (typeof FeedbackValueType)[keyof typeof FeedbackValueType];
export interface EventFeedbackType {
  FeedbackValue: FeedbackValueType | undefined;
  Provider: string | undefined;
  FeedbackDate?: Date | undefined;
}
export declare const EventResponseType: {
  readonly Fail: "Fail";
  readonly InProgress: "InProgress";
  readonly Pass: "Pass";
};
export type EventResponseType =
  (typeof EventResponseType)[keyof typeof EventResponseType];
export declare const RiskDecisionType: {
  readonly AccountTakeover: "AccountTakeover";
  readonly Block: "Block";
  readonly NoRisk: "NoRisk";
};
export type RiskDecisionType =
  (typeof RiskDecisionType)[keyof typeof RiskDecisionType];
export declare const RiskLevelType: {
  readonly High: "High";
  readonly Low: "Low";
  readonly Medium: "Medium";
};
export type RiskLevelType = (typeof RiskLevelType)[keyof typeof RiskLevelType];
export interface EventRiskType {
  RiskDecision?: RiskDecisionType | undefined;
  RiskLevel?: RiskLevelType | undefined;
  CompromisedCredentialsDetected?: boolean | undefined;
}
export declare const EventType: {
  readonly ForgotPassword: "ForgotPassword";
  readonly PasswordChange: "PasswordChange";
  readonly ResendCode: "ResendCode";
  readonly SignIn: "SignIn";
  readonly SignUp: "SignUp";
};
export type EventType = (typeof EventType)[keyof typeof EventType];
export interface AuthEventType {
  EventId?: string | undefined;
  EventType?: EventType | undefined;
  CreationDate?: Date | undefined;
  EventResponse?: EventResponseType | undefined;
  EventRisk?: EventRiskType | undefined;
  ChallengeResponses?: ChallengeResponseType[] | undefined;
  EventContextData?: EventContextDataType | undefined;
  EventFeedback?: EventFeedbackType | undefined;
}
export interface AdminListUserAuthEventsResponse {
  AuthEvents?: AuthEventType[] | undefined;
  NextToken?: string | undefined;
}
export declare class UserPoolAddOnNotEnabledException extends __BaseException {
  readonly name: "UserPoolAddOnNotEnabledException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      UserPoolAddOnNotEnabledException,
      __BaseException
    >
  );
}
export interface AdminRemoveUserFromGroupRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  GroupName: string | undefined;
}
export interface AdminResetUserPasswordRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  ClientMetadata?: Record<string, string> | undefined;
}
export interface AdminResetUserPasswordResponse {}
export interface AdminRespondToAuthChallengeRequest {
  UserPoolId: string | undefined;
  ClientId: string | undefined;
  ChallengeName: ChallengeNameType | undefined;
  ChallengeResponses?: Record<string, string> | undefined;
  Session?: string | undefined;
  AnalyticsMetadata?: AnalyticsMetadataType | undefined;
  ContextData?: ContextDataType | undefined;
  ClientMetadata?: Record<string, string> | undefined;
}
export interface AdminRespondToAuthChallengeResponse {
  ChallengeName?: ChallengeNameType | undefined;
  Session?: string | undefined;
  ChallengeParameters?: Record<string, string> | undefined;
  AuthenticationResult?: AuthenticationResultType | undefined;
}
export declare class CodeMismatchException extends __BaseException {
  readonly name: "CodeMismatchException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<CodeMismatchException, __BaseException>
  );
}
export declare class ExpiredCodeException extends __BaseException {
  readonly name: "ExpiredCodeException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<ExpiredCodeException, __BaseException>
  );
}
export declare class PasswordHistoryPolicyViolationException extends __BaseException {
  readonly name: "PasswordHistoryPolicyViolationException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      PasswordHistoryPolicyViolationException,
      __BaseException
    >
  );
}
export declare class SoftwareTokenMFANotFoundException extends __BaseException {
  readonly name: "SoftwareTokenMFANotFoundException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      SoftwareTokenMFANotFoundException,
      __BaseException
    >
  );
}
export interface EmailMfaSettingsType {
  Enabled?: boolean | undefined;
  PreferredMfa?: boolean | undefined;
}
export interface SMSMfaSettingsType {
  Enabled?: boolean | undefined;
  PreferredMfa?: boolean | undefined;
}
export interface SoftwareTokenMfaSettingsType {
  Enabled?: boolean | undefined;
  PreferredMfa?: boolean | undefined;
}
export interface AdminSetUserMFAPreferenceRequest {
  SMSMfaSettings?: SMSMfaSettingsType | undefined;
  SoftwareTokenMfaSettings?: SoftwareTokenMfaSettingsType | undefined;
  EmailMfaSettings?: EmailMfaSettingsType | undefined;
  Username: string | undefined;
  UserPoolId: string | undefined;
}
export interface AdminSetUserMFAPreferenceResponse {}
export interface AdminSetUserPasswordRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  Password: string | undefined;
  Permanent?: boolean | undefined;
}
export interface AdminSetUserPasswordResponse {}
export interface AdminSetUserSettingsRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  MFAOptions: MFAOptionType[] | undefined;
}
export interface AdminSetUserSettingsResponse {}
export interface AdminUpdateAuthEventFeedbackRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  EventId: string | undefined;
  FeedbackValue: FeedbackValueType | undefined;
}
export interface AdminUpdateAuthEventFeedbackResponse {}
export declare const DeviceRememberedStatusType: {
  readonly NOT_REMEMBERED: "not_remembered";
  readonly REMEMBERED: "remembered";
};
export type DeviceRememberedStatusType =
  (typeof DeviceRememberedStatusType)[keyof typeof DeviceRememberedStatusType];
export interface AdminUpdateDeviceStatusRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  DeviceKey: string | undefined;
  DeviceRememberedStatus?: DeviceRememberedStatusType | undefined;
}
export interface AdminUpdateDeviceStatusResponse {}
export interface AdminUpdateUserAttributesRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  UserAttributes: AttributeType[] | undefined;
  ClientMetadata?: Record<string, string> | undefined;
}
export interface AdminUpdateUserAttributesResponse {}
export interface AdminUserGlobalSignOutRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
}
export interface AdminUserGlobalSignOutResponse {}
export declare const AdvancedSecurityEnabledModeType: {
  readonly AUDIT: "AUDIT";
  readonly ENFORCED: "ENFORCED";
};
export type AdvancedSecurityEnabledModeType =
  (typeof AdvancedSecurityEnabledModeType)[keyof typeof AdvancedSecurityEnabledModeType];
export interface AdvancedSecurityAdditionalFlowsType {
  CustomAuthMode?: AdvancedSecurityEnabledModeType | undefined;
}
export declare const AdvancedSecurityModeType: {
  readonly AUDIT: "AUDIT";
  readonly ENFORCED: "ENFORCED";
  readonly OFF: "OFF";
};
export type AdvancedSecurityModeType =
  (typeof AdvancedSecurityModeType)[keyof typeof AdvancedSecurityModeType];
export declare const AliasAttributeType: {
  readonly EMAIL: "email";
  readonly PHONE_NUMBER: "phone_number";
  readonly PREFERRED_USERNAME: "preferred_username";
};
export type AliasAttributeType =
  (typeof AliasAttributeType)[keyof typeof AliasAttributeType];
export declare const AuthFactorType: {
  readonly EMAIL_OTP: "EMAIL_OTP";
  readonly PASSWORD: "PASSWORD";
  readonly SMS_OTP: "SMS_OTP";
  readonly WEB_AUTHN: "WEB_AUTHN";
};
export type AuthFactorType =
  (typeof AuthFactorType)[keyof typeof AuthFactorType];
export interface AnalyticsConfigurationType {
  ApplicationId?: string | undefined;
  ApplicationArn?: string | undefined;
  RoleArn?: string | undefined;
  ExternalId?: string | undefined;
  UserDataShared?: boolean | undefined;
}
export declare const AssetCategoryType: {
  readonly AUTH_APP_GRAPHIC: "AUTH_APP_GRAPHIC";
  readonly EMAIL_GRAPHIC: "EMAIL_GRAPHIC";
  readonly FAVICON_ICO: "FAVICON_ICO";
  readonly FAVICON_SVG: "FAVICON_SVG";
  readonly FORM_BACKGROUND: "FORM_BACKGROUND";
  readonly FORM_LOGO: "FORM_LOGO";
  readonly IDP_BUTTON_ICON: "IDP_BUTTON_ICON";
  readonly PAGE_BACKGROUND: "PAGE_BACKGROUND";
  readonly PAGE_FOOTER_BACKGROUND: "PAGE_FOOTER_BACKGROUND";
  readonly PAGE_FOOTER_LOGO: "PAGE_FOOTER_LOGO";
  readonly PAGE_HEADER_BACKGROUND: "PAGE_HEADER_BACKGROUND";
  readonly PAGE_HEADER_LOGO: "PAGE_HEADER_LOGO";
  readonly PASSKEY_GRAPHIC: "PASSKEY_GRAPHIC";
  readonly PASSWORD_GRAPHIC: "PASSWORD_GRAPHIC";
  readonly SMS_GRAPHIC: "SMS_GRAPHIC";
};
export type AssetCategoryType =
  (typeof AssetCategoryType)[keyof typeof AssetCategoryType];
export declare const AssetExtensionType: {
  readonly ICO: "ICO";
  readonly JPEG: "JPEG";
  readonly PNG: "PNG";
  readonly SVG: "SVG";
  readonly WEBP: "WEBP";
};
export type AssetExtensionType =
  (typeof AssetExtensionType)[keyof typeof AssetExtensionType];
export declare const ColorSchemeModeType: {
  readonly DARK: "DARK";
  readonly DYNAMIC: "DYNAMIC";
  readonly LIGHT: "LIGHT";
};
export type ColorSchemeModeType =
  (typeof ColorSchemeModeType)[keyof typeof ColorSchemeModeType];
export interface AssetType {
  Category: AssetCategoryType | undefined;
  ColorMode: ColorSchemeModeType | undefined;
  Extension: AssetExtensionType | undefined;
  Bytes?: Uint8Array | undefined;
  ResourceId?: string | undefined;
}
export interface AssociateSoftwareTokenRequest {
  AccessToken?: string | undefined;
  Session?: string | undefined;
}
export interface AssociateSoftwareTokenResponse {
  SecretCode?: string | undefined;
  Session?: string | undefined;
}
export declare class ConcurrentModificationException extends __BaseException {
  readonly name: "ConcurrentModificationException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      ConcurrentModificationException,
      __BaseException
    >
  );
}
export declare class ForbiddenException extends __BaseException {
  readonly name: "ForbiddenException";
  readonly $fault: "client";
  constructor(opts: __ExceptionOptionType<ForbiddenException, __BaseException>);
}
export declare const VerifiedAttributeType: {
  readonly EMAIL: "email";
  readonly PHONE_NUMBER: "phone_number";
};
export type VerifiedAttributeType =
  (typeof VerifiedAttributeType)[keyof typeof VerifiedAttributeType];
export interface ChangePasswordRequest {
  PreviousPassword?: string | undefined;
  ProposedPassword: string | undefined;
  AccessToken: string | undefined;
}
export interface ChangePasswordResponse {}
export interface CompleteWebAuthnRegistrationRequest {
  AccessToken: string | undefined;
  Credential: __DocumentType | undefined;
}
export interface CompleteWebAuthnRegistrationResponse {}
export declare class WebAuthnChallengeNotFoundException extends __BaseException {
  readonly name: "WebAuthnChallengeNotFoundException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      WebAuthnChallengeNotFoundException,
      __BaseException
    >
  );
}
export declare class WebAuthnClientMismatchException extends __BaseException {
  readonly name: "WebAuthnClientMismatchException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      WebAuthnClientMismatchException,
      __BaseException
    >
  );
}
export declare class WebAuthnCredentialNotSupportedException extends __BaseException {
  readonly name: "WebAuthnCredentialNotSupportedException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      WebAuthnCredentialNotSupportedException,
      __BaseException
    >
  );
}
export declare class WebAuthnNotEnabledException extends __BaseException {
  readonly name: "WebAuthnNotEnabledException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<WebAuthnNotEnabledException, __BaseException>
  );
}
export declare class WebAuthnOriginNotAllowedException extends __BaseException {
  readonly name: "WebAuthnOriginNotAllowedException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      WebAuthnOriginNotAllowedException,
      __BaseException
    >
  );
}
export declare class WebAuthnRelyingPartyMismatchException extends __BaseException {
  readonly name: "WebAuthnRelyingPartyMismatchException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      WebAuthnRelyingPartyMismatchException,
      __BaseException
    >
  );
}
export interface DeviceSecretVerifierConfigType {
  PasswordVerifier?: string | undefined;
  Salt?: string | undefined;
}
export interface ConfirmDeviceRequest {
  AccessToken: string | undefined;
  DeviceKey: string | undefined;
  DeviceSecretVerifierConfig?: DeviceSecretVerifierConfigType | undefined;
  DeviceName?: string | undefined;
}
export interface ConfirmDeviceResponse {
  UserConfirmationNecessary?: boolean | undefined;
}
export declare class DeviceKeyExistsException extends __BaseException {
  readonly name: "DeviceKeyExistsException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<DeviceKeyExistsException, __BaseException>
  );
}
export interface UserContextDataType {
  IpAddress?: string | undefined;
  EncodedData?: string | undefined;
}
export interface ConfirmForgotPasswordRequest {
  ClientId: string | undefined;
  SecretHash?: string | undefined;
  Username: string | undefined;
  ConfirmationCode: string | undefined;
  Password: string | undefined;
  AnalyticsMetadata?: AnalyticsMetadataType | undefined;
  UserContextData?: UserContextDataType | undefined;
  ClientMetadata?: Record<string, string> | undefined;
}
export interface ConfirmForgotPasswordResponse {}
export interface ConfirmSignUpRequest {
  ClientId: string | undefined;
  SecretHash?: string | undefined;
  Username: string | undefined;
  ConfirmationCode: string | undefined;
  ForceAliasCreation?: boolean | undefined;
  AnalyticsMetadata?: AnalyticsMetadataType | undefined;
  UserContextData?: UserContextDataType | undefined;
  ClientMetadata?: Record<string, string> | undefined;
  Session?: string | undefined;
}
export interface ConfirmSignUpResponse {
  Session?: string | undefined;
}
export interface CreateGroupRequest {
  GroupName: string | undefined;
  UserPoolId: string | undefined;
  Description?: string | undefined;
  RoleArn?: string | undefined;
  Precedence?: number | undefined;
}
export interface CreateGroupResponse {
  Group?: GroupType | undefined;
}
export declare class GroupExistsException extends __BaseException {
  readonly name: "GroupExistsException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<GroupExistsException, __BaseException>
  );
}
export declare const IdentityProviderTypeType: {
  readonly Facebook: "Facebook";
  readonly Google: "Google";
  readonly LoginWithAmazon: "LoginWithAmazon";
  readonly OIDC: "OIDC";
  readonly SAML: "SAML";
  readonly SignInWithApple: "SignInWithApple";
};
export type IdentityProviderTypeType =
  (typeof IdentityProviderTypeType)[keyof typeof IdentityProviderTypeType];
export interface CreateIdentityProviderRequest {
  UserPoolId: string | undefined;
  ProviderName: string | undefined;
  ProviderType: IdentityProviderTypeType | undefined;
  ProviderDetails: Record<string, string> | undefined;
  AttributeMapping?: Record<string, string> | undefined;
  IdpIdentifiers?: string[] | undefined;
}
export interface IdentityProviderType {
  UserPoolId?: string | undefined;
  ProviderName?: string | undefined;
  ProviderType?: IdentityProviderTypeType | undefined;
  ProviderDetails?: Record<string, string> | undefined;
  AttributeMapping?: Record<string, string> | undefined;
  IdpIdentifiers?: string[] | undefined;
  LastModifiedDate?: Date | undefined;
  CreationDate?: Date | undefined;
}
export interface CreateIdentityProviderResponse {
  IdentityProvider: IdentityProviderType | undefined;
}
export declare class DuplicateProviderException extends __BaseException {
  readonly name: "DuplicateProviderException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<DuplicateProviderException, __BaseException>
  );
}
export interface CreateManagedLoginBrandingRequest {
  UserPoolId: string | undefined;
  ClientId: string | undefined;
  UseCognitoProvidedValues?: boolean | undefined;
  Settings?: __DocumentType | undefined;
  Assets?: AssetType[] | undefined;
}
export interface ManagedLoginBrandingType {
  ManagedLoginBrandingId?: string | undefined;
  UserPoolId?: string | undefined;
  UseCognitoProvidedValues?: boolean | undefined;
  Settings?: __DocumentType | undefined;
  Assets?: AssetType[] | undefined;
  CreationDate?: Date | undefined;
  LastModifiedDate?: Date | undefined;
}
export interface CreateManagedLoginBrandingResponse {
  ManagedLoginBranding?: ManagedLoginBrandingType | undefined;
}
export declare class ManagedLoginBrandingExistsException extends __BaseException {
  readonly name: "ManagedLoginBrandingExistsException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      ManagedLoginBrandingExistsException,
      __BaseException
    >
  );
}
export interface ResourceServerScopeType {
  ScopeName: string | undefined;
  ScopeDescription: string | undefined;
}
export interface CreateResourceServerRequest {
  UserPoolId: string | undefined;
  Identifier: string | undefined;
  Name: string | undefined;
  Scopes?: ResourceServerScopeType[] | undefined;
}
export interface ResourceServerType {
  UserPoolId?: string | undefined;
  Identifier?: string | undefined;
  Name?: string | undefined;
  Scopes?: ResourceServerScopeType[] | undefined;
}
export interface CreateResourceServerResponse {
  ResourceServer: ResourceServerType | undefined;
}
export interface CreateUserImportJobRequest {
  JobName: string | undefined;
  UserPoolId: string | undefined;
  CloudWatchLogsRoleArn: string | undefined;
}
export declare const UserImportJobStatusType: {
  readonly Created: "Created";
  readonly Expired: "Expired";
  readonly Failed: "Failed";
  readonly InProgress: "InProgress";
  readonly Pending: "Pending";
  readonly Stopped: "Stopped";
  readonly Stopping: "Stopping";
  readonly Succeeded: "Succeeded";
};
export type UserImportJobStatusType =
  (typeof UserImportJobStatusType)[keyof typeof UserImportJobStatusType];
export interface UserImportJobType {
  JobName?: string | undefined;
  JobId?: string | undefined;
  UserPoolId?: string | undefined;
  PreSignedUrl?: string | undefined;
  CreationDate?: Date | undefined;
  StartDate?: Date | undefined;
  CompletionDate?: Date | undefined;
  Status?: UserImportJobStatusType | undefined;
  CloudWatchLogsRoleArn?: string | undefined;
  ImportedUsers?: number | undefined;
  SkippedUsers?: number | undefined;
  FailedUsers?: number | undefined;
  CompletionMessage?: string | undefined;
}
export interface CreateUserImportJobResponse {
  UserImportJob?: UserImportJobType | undefined;
}
export declare const DeletionProtectionType: {
  readonly ACTIVE: "ACTIVE";
  readonly INACTIVE: "INACTIVE";
};
export type DeletionProtectionType =
  (typeof DeletionProtectionType)[keyof typeof DeletionProtectionType];
export interface DeviceConfigurationType {
  ChallengeRequiredOnNewDevice?: boolean | undefined;
  DeviceOnlyRememberedOnUserPrompt?: boolean | undefined;
}
export declare const EmailSendingAccountType: {
  readonly COGNITO_DEFAULT: "COGNITO_DEFAULT";
  readonly DEVELOPER: "DEVELOPER";
};
export type EmailSendingAccountType =
  (typeof EmailSendingAccountType)[keyof typeof EmailSendingAccountType];
export interface EmailConfigurationType {
  SourceArn?: string | undefined;
  ReplyToEmailAddress?: string | undefined;
  EmailSendingAccount?: EmailSendingAccountType | undefined;
  From?: string | undefined;
  ConfigurationSet?: string | undefined;
}
export declare const CustomEmailSenderLambdaVersionType: {
  readonly V1_0: "V1_0";
};
export type CustomEmailSenderLambdaVersionType =
  (typeof CustomEmailSenderLambdaVersionType)[keyof typeof CustomEmailSenderLambdaVersionType];
export interface CustomEmailLambdaVersionConfigType {
  LambdaVersion: CustomEmailSenderLambdaVersionType | undefined;
  LambdaArn: string | undefined;
}
export declare const CustomSMSSenderLambdaVersionType: {
  readonly V1_0: "V1_0";
};
export type CustomSMSSenderLambdaVersionType =
  (typeof CustomSMSSenderLambdaVersionType)[keyof typeof CustomSMSSenderLambdaVersionType];
export interface CustomSMSLambdaVersionConfigType {
  LambdaVersion: CustomSMSSenderLambdaVersionType | undefined;
  LambdaArn: string | undefined;
}
export declare const PreTokenGenerationLambdaVersionType: {
  readonly V1_0: "V1_0";
  readonly V2_0: "V2_0";
  readonly V3_0: "V3_0";
};
export type PreTokenGenerationLambdaVersionType =
  (typeof PreTokenGenerationLambdaVersionType)[keyof typeof PreTokenGenerationLambdaVersionType];
export interface PreTokenGenerationVersionConfigType {
  LambdaVersion: PreTokenGenerationLambdaVersionType | undefined;
  LambdaArn: string | undefined;
}
export interface LambdaConfigType {
  PreSignUp?: string | undefined;
  CustomMessage?: string | undefined;
  PostConfirmation?: string | undefined;
  PreAuthentication?: string | undefined;
  PostAuthentication?: string | undefined;
  DefineAuthChallenge?: string | undefined;
  CreateAuthChallenge?: string | undefined;
  VerifyAuthChallengeResponse?: string | undefined;
  PreTokenGeneration?: string | undefined;
  UserMigration?: string | undefined;
  PreTokenGenerationConfig?: PreTokenGenerationVersionConfigType | undefined;
  CustomSMSSender?: CustomSMSLambdaVersionConfigType | undefined;
  CustomEmailSender?: CustomEmailLambdaVersionConfigType | undefined;
  KMSKeyID?: string | undefined;
}
export declare const UserPoolMfaType: {
  readonly OFF: "OFF";
  readonly ON: "ON";
  readonly OPTIONAL: "OPTIONAL";
};
export type UserPoolMfaType =
  (typeof UserPoolMfaType)[keyof typeof UserPoolMfaType];
export interface PasswordPolicyType {
  MinimumLength?: number | undefined;
  RequireUppercase?: boolean | undefined;
  RequireLowercase?: boolean | undefined;
  RequireNumbers?: boolean | undefined;
  RequireSymbols?: boolean | undefined;
  PasswordHistorySize?: number | undefined;
  TemporaryPasswordValidityDays?: number | undefined;
}
export interface SignInPolicyType {
  AllowedFirstAuthFactors?: AuthFactorType[] | undefined;
}
export interface UserPoolPolicyType {
  PasswordPolicy?: PasswordPolicyType | undefined;
  SignInPolicy?: SignInPolicyType | undefined;
}
export interface SmsConfigurationType {
  SnsCallerArn: string | undefined;
  ExternalId?: string | undefined;
  SnsRegion?: string | undefined;
}
export interface UserAttributeUpdateSettingsType {
  AttributesRequireVerificationBeforeUpdate?:
    | VerifiedAttributeType[]
    | undefined;
}
export declare const UsernameAttributeType: {
  readonly EMAIL: "email";
  readonly PHONE_NUMBER: "phone_number";
};
export type UsernameAttributeType =
  (typeof UsernameAttributeType)[keyof typeof UsernameAttributeType];
export interface UsernameConfigurationType {
  CaseSensitive: boolean | undefined;
}
export interface UserPoolAddOnsType {
  AdvancedSecurityMode: AdvancedSecurityModeType | undefined;
  AdvancedSecurityAdditionalFlows?:
    | AdvancedSecurityAdditionalFlowsType
    | undefined;
}
export declare const UserPoolTierType: {
  readonly ESSENTIALS: "ESSENTIALS";
  readonly LITE: "LITE";
  readonly PLUS: "PLUS";
};
export type UserPoolTierType =
  (typeof UserPoolTierType)[keyof typeof UserPoolTierType];
export declare const DefaultEmailOptionType: {
  readonly CONFIRM_WITH_CODE: "CONFIRM_WITH_CODE";
  readonly CONFIRM_WITH_LINK: "CONFIRM_WITH_LINK";
};
export type DefaultEmailOptionType =
  (typeof DefaultEmailOptionType)[keyof typeof DefaultEmailOptionType];
export interface VerificationMessageTemplateType {
  SmsMessage?: string | undefined;
  EmailMessage?: string | undefined;
  EmailSubject?: string | undefined;
  EmailMessageByLink?: string | undefined;
  EmailSubjectByLink?: string | undefined;
  DefaultEmailOption?: DefaultEmailOptionType | undefined;
}
export interface CreateUserPoolRequest {
  PoolName: string | undefined;
  Policies?: UserPoolPolicyType | undefined;
  DeletionProtection?: DeletionProtectionType | undefined;
  LambdaConfig?: LambdaConfigType | undefined;
  AutoVerifiedAttributes?: VerifiedAttributeType[] | undefined;
  AliasAttributes?: AliasAttributeType[] | undefined;
  UsernameAttributes?: UsernameAttributeType[] | undefined;
  SmsVerificationMessage?: string | undefined;
  EmailVerificationMessage?: string | undefined;
  EmailVerificationSubject?: string | undefined;
  VerificationMessageTemplate?: VerificationMessageTemplateType | undefined;
  SmsAuthenticationMessage?: string | undefined;
  MfaConfiguration?: UserPoolMfaType | undefined;
  UserAttributeUpdateSettings?: UserAttributeUpdateSettingsType | undefined;
  DeviceConfiguration?: DeviceConfigurationType | undefined;
  EmailConfiguration?: EmailConfigurationType | undefined;
  SmsConfiguration?: SmsConfigurationType | undefined;
  UserPoolTags?: Record<string, string> | undefined;
  AdminCreateUserConfig?: AdminCreateUserConfigType | undefined;
  Schema?: SchemaAttributeType[] | undefined;
  UserPoolAddOns?: UserPoolAddOnsType | undefined;
  UsernameConfiguration?: UsernameConfigurationType | undefined;
  AccountRecoverySetting?: AccountRecoverySettingType | undefined;
  UserPoolTier?: UserPoolTierType | undefined;
}
export declare const StatusType: {
  readonly Disabled: "Disabled";
  readonly Enabled: "Enabled";
};
export type StatusType = (typeof StatusType)[keyof typeof StatusType];
export interface UserPoolType {
  Id?: string | undefined;
  Name?: string | undefined;
  Policies?: UserPoolPolicyType | undefined;
  DeletionProtection?: DeletionProtectionType | undefined;
  LambdaConfig?: LambdaConfigType | undefined;
  Status?: StatusType | undefined;
  LastModifiedDate?: Date | undefined;
  CreationDate?: Date | undefined;
  SchemaAttributes?: SchemaAttributeType[] | undefined;
  AutoVerifiedAttributes?: VerifiedAttributeType[] | undefined;
  AliasAttributes?: AliasAttributeType[] | undefined;
  UsernameAttributes?: UsernameAttributeType[] | undefined;
  SmsVerificationMessage?: string | undefined;
  EmailVerificationMessage?: string | undefined;
  EmailVerificationSubject?: string | undefined;
  VerificationMessageTemplate?: VerificationMessageTemplateType | undefined;
  SmsAuthenticationMessage?: string | undefined;
  UserAttributeUpdateSettings?: UserAttributeUpdateSettingsType | undefined;
  MfaConfiguration?: UserPoolMfaType | undefined;
  DeviceConfiguration?: DeviceConfigurationType | undefined;
  EstimatedNumberOfUsers?: number | undefined;
  EmailConfiguration?: EmailConfigurationType | undefined;
  SmsConfiguration?: SmsConfigurationType | undefined;
  UserPoolTags?: Record<string, string> | undefined;
  SmsConfigurationFailure?: string | undefined;
  EmailConfigurationFailure?: string | undefined;
  Domain?: string | undefined;
  CustomDomain?: string | undefined;
  AdminCreateUserConfig?: AdminCreateUserConfigType | undefined;
  UserPoolAddOns?: UserPoolAddOnsType | undefined;
  UsernameConfiguration?: UsernameConfigurationType | undefined;
  Arn?: string | undefined;
  AccountRecoverySetting?: AccountRecoverySettingType | undefined;
  UserPoolTier?: UserPoolTierType | undefined;
}
export interface CreateUserPoolResponse {
  UserPool?: UserPoolType | undefined;
}
export declare class FeatureUnavailableInTierException extends __BaseException {
  readonly name: "FeatureUnavailableInTierException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      FeatureUnavailableInTierException,
      __BaseException
    >
  );
}
export declare class TierChangeNotAllowedException extends __BaseException {
  readonly name: "TierChangeNotAllowedException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<TierChangeNotAllowedException, __BaseException>
  );
}
export declare class UserPoolTaggingException extends __BaseException {
  readonly name: "UserPoolTaggingException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<UserPoolTaggingException, __BaseException>
  );
}
export declare const OAuthFlowType: {
  readonly client_credentials: "client_credentials";
  readonly code: "code";
  readonly implicit: "implicit";
};
export type OAuthFlowType = (typeof OAuthFlowType)[keyof typeof OAuthFlowType];
export declare const ExplicitAuthFlowsType: {
  readonly ADMIN_NO_SRP_AUTH: "ADMIN_NO_SRP_AUTH";
  readonly ALLOW_ADMIN_USER_PASSWORD_AUTH: "ALLOW_ADMIN_USER_PASSWORD_AUTH";
  readonly ALLOW_CUSTOM_AUTH: "ALLOW_CUSTOM_AUTH";
  readonly ALLOW_REFRESH_TOKEN_AUTH: "ALLOW_REFRESH_TOKEN_AUTH";
  readonly ALLOW_USER_AUTH: "ALLOW_USER_AUTH";
  readonly ALLOW_USER_PASSWORD_AUTH: "ALLOW_USER_PASSWORD_AUTH";
  readonly ALLOW_USER_SRP_AUTH: "ALLOW_USER_SRP_AUTH";
  readonly CUSTOM_AUTH_FLOW_ONLY: "CUSTOM_AUTH_FLOW_ONLY";
  readonly USER_PASSWORD_AUTH: "USER_PASSWORD_AUTH";
};
export type ExplicitAuthFlowsType =
  (typeof ExplicitAuthFlowsType)[keyof typeof ExplicitAuthFlowsType];
export declare const PreventUserExistenceErrorTypes: {
  readonly ENABLED: "ENABLED";
  readonly LEGACY: "LEGACY";
};
export type PreventUserExistenceErrorTypes =
  (typeof PreventUserExistenceErrorTypes)[keyof typeof PreventUserExistenceErrorTypes];
export declare const FeatureType: {
  readonly DISABLED: "DISABLED";
  readonly ENABLED: "ENABLED";
};
export type FeatureType = (typeof FeatureType)[keyof typeof FeatureType];
export interface RefreshTokenRotationType {
  Feature: FeatureType | undefined;
  RetryGracePeriodSeconds?: number | undefined;
}
export declare const TimeUnitsType: {
  readonly DAYS: "days";
  readonly HOURS: "hours";
  readonly MINUTES: "minutes";
  readonly SECONDS: "seconds";
};
export type TimeUnitsType = (typeof TimeUnitsType)[keyof typeof TimeUnitsType];
export interface TokenValidityUnitsType {
  AccessToken?: TimeUnitsType | undefined;
  IdToken?: TimeUnitsType | undefined;
  RefreshToken?: TimeUnitsType | undefined;
}
export interface CreateUserPoolClientRequest {
  UserPoolId: string | undefined;
  ClientName: string | undefined;
  GenerateSecret?: boolean | undefined;
  RefreshTokenValidity?: number | undefined;
  AccessTokenValidity?: number | undefined;
  IdTokenValidity?: number | undefined;
  TokenValidityUnits?: TokenValidityUnitsType | undefined;
  ReadAttributes?: string[] | undefined;
  WriteAttributes?: string[] | undefined;
  ExplicitAuthFlows?: ExplicitAuthFlowsType[] | undefined;
  SupportedIdentityProviders?: string[] | undefined;
  CallbackURLs?: string[] | undefined;
  LogoutURLs?: string[] | undefined;
  DefaultRedirectURI?: string | undefined;
  AllowedOAuthFlows?: OAuthFlowType[] | undefined;
  AllowedOAuthScopes?: string[] | undefined;
  AllowedOAuthFlowsUserPoolClient?: boolean | undefined;
  AnalyticsConfiguration?: AnalyticsConfigurationType | undefined;
  PreventUserExistenceErrors?: PreventUserExistenceErrorTypes | undefined;
  EnableTokenRevocation?: boolean | undefined;
  EnablePropagateAdditionalUserContextData?: boolean | undefined;
  AuthSessionValidity?: number | undefined;
  RefreshTokenRotation?: RefreshTokenRotationType | undefined;
}
export interface UserPoolClientType {
  UserPoolId?: string | undefined;
  ClientName?: string | undefined;
  ClientId?: string | undefined;
  ClientSecret?: string | undefined;
  LastModifiedDate?: Date | undefined;
  CreationDate?: Date | undefined;
  RefreshTokenValidity?: number | undefined;
  AccessTokenValidity?: number | undefined;
  IdTokenValidity?: number | undefined;
  TokenValidityUnits?: TokenValidityUnitsType | undefined;
  ReadAttributes?: string[] | undefined;
  WriteAttributes?: string[] | undefined;
  ExplicitAuthFlows?: ExplicitAuthFlowsType[] | undefined;
  SupportedIdentityProviders?: string[] | undefined;
  CallbackURLs?: string[] | undefined;
  LogoutURLs?: string[] | undefined;
  DefaultRedirectURI?: string | undefined;
  AllowedOAuthFlows?: OAuthFlowType[] | undefined;
  AllowedOAuthScopes?: string[] | undefined;
  AllowedOAuthFlowsUserPoolClient?: boolean | undefined;
  AnalyticsConfiguration?: AnalyticsConfigurationType | undefined;
  PreventUserExistenceErrors?: PreventUserExistenceErrorTypes | undefined;
  EnableTokenRevocation?: boolean | undefined;
  EnablePropagateAdditionalUserContextData?: boolean | undefined;
  AuthSessionValidity?: number | undefined;
  RefreshTokenRotation?: RefreshTokenRotationType | undefined;
}
export interface CreateUserPoolClientResponse {
  UserPoolClient?: UserPoolClientType | undefined;
}
export declare class InvalidOAuthFlowException extends __BaseException {
  readonly name: "InvalidOAuthFlowException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<InvalidOAuthFlowException, __BaseException>
  );
}
export declare class ScopeDoesNotExistException extends __BaseException {
  readonly name: "ScopeDoesNotExistException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<ScopeDoesNotExistException, __BaseException>
  );
}
export interface CustomDomainConfigType {
  CertificateArn: string | undefined;
}
export interface CreateUserPoolDomainRequest {
  Domain: string | undefined;
  UserPoolId: string | undefined;
  ManagedLoginVersion?: number | undefined;
  CustomDomainConfig?: CustomDomainConfigType | undefined;
}
export interface CreateUserPoolDomainResponse {
  ManagedLoginVersion?: number | undefined;
  CloudFrontDomain?: string | undefined;
}
export interface DeleteGroupRequest {
  GroupName: string | undefined;
  UserPoolId: string | undefined;
}
export interface DeleteIdentityProviderRequest {
  UserPoolId: string | undefined;
  ProviderName: string | undefined;
}
export declare class UnsupportedIdentityProviderException extends __BaseException {
  readonly name: "UnsupportedIdentityProviderException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      UnsupportedIdentityProviderException,
      __BaseException
    >
  );
}
export interface DeleteManagedLoginBrandingRequest {
  ManagedLoginBrandingId: string | undefined;
  UserPoolId: string | undefined;
}
export interface DeleteResourceServerRequest {
  UserPoolId: string | undefined;
  Identifier: string | undefined;
}
export interface DeleteUserRequest {
  AccessToken: string | undefined;
}
export interface DeleteUserAttributesRequest {
  UserAttributeNames: string[] | undefined;
  AccessToken: string | undefined;
}
export interface DeleteUserAttributesResponse {}
export interface DeleteUserPoolRequest {
  UserPoolId: string | undefined;
}
export interface DeleteUserPoolClientRequest {
  UserPoolId: string | undefined;
  ClientId: string | undefined;
}
export interface DeleteUserPoolDomainRequest {
  Domain: string | undefined;
  UserPoolId: string | undefined;
}
export interface DeleteUserPoolDomainResponse {}
export interface DeleteWebAuthnCredentialRequest {
  AccessToken: string | undefined;
  CredentialId: string | undefined;
}
export interface DeleteWebAuthnCredentialResponse {}
export interface DescribeIdentityProviderRequest {
  UserPoolId: string | undefined;
  ProviderName: string | undefined;
}
export interface DescribeIdentityProviderResponse {
  IdentityProvider: IdentityProviderType | undefined;
}
export interface DescribeManagedLoginBrandingRequest {
  UserPoolId: string | undefined;
  ManagedLoginBrandingId: string | undefined;
  ReturnMergedResources?: boolean | undefined;
}
export interface DescribeManagedLoginBrandingResponse {
  ManagedLoginBranding?: ManagedLoginBrandingType | undefined;
}
export interface DescribeManagedLoginBrandingByClientRequest {
  UserPoolId: string | undefined;
  ClientId: string | undefined;
  ReturnMergedResources?: boolean | undefined;
}
export interface DescribeManagedLoginBrandingByClientResponse {
  ManagedLoginBranding?: ManagedLoginBrandingType | undefined;
}
export interface DescribeResourceServerRequest {
  UserPoolId: string | undefined;
  Identifier: string | undefined;
}
export interface DescribeResourceServerResponse {
  ResourceServer: ResourceServerType | undefined;
}
export interface DescribeRiskConfigurationRequest {
  UserPoolId: string | undefined;
  ClientId?: string | undefined;
}
export declare const CompromisedCredentialsEventActionType: {
  readonly BLOCK: "BLOCK";
  readonly NO_ACTION: "NO_ACTION";
};
export type CompromisedCredentialsEventActionType =
  (typeof CompromisedCredentialsEventActionType)[keyof typeof CompromisedCredentialsEventActionType];
export interface CompromisedCredentialsActionsType {
  EventAction: CompromisedCredentialsEventActionType | undefined;
}
export declare const EventFilterType: {
  readonly PASSWORD_CHANGE: "PASSWORD_CHANGE";
  readonly SIGN_IN: "SIGN_IN";
  readonly SIGN_UP: "SIGN_UP";
};
export type EventFilterType =
  (typeof EventFilterType)[keyof typeof EventFilterType];
export interface CompromisedCredentialsRiskConfigurationType {
  EventFilter?: EventFilterType[] | undefined;
  Actions: CompromisedCredentialsActionsType | undefined;
}
export interface RiskExceptionConfigurationType {
  BlockedIPRangeList?: string[] | undefined;
  SkippedIPRangeList?: string[] | undefined;
}
export interface RiskConfigurationType {
  UserPoolId?: string | undefined;
  ClientId?: string | undefined;
  CompromisedCredentialsRiskConfiguration?:
    | CompromisedCredentialsRiskConfigurationType
    | undefined;
  AccountTakeoverRiskConfiguration?:
    | AccountTakeoverRiskConfigurationType
    | undefined;
  RiskExceptionConfiguration?: RiskExceptionConfigurationType | undefined;
  LastModifiedDate?: Date | undefined;
}
export interface DescribeRiskConfigurationResponse {
  RiskConfiguration: RiskConfigurationType | undefined;
}
export interface DescribeUserImportJobRequest {
  UserPoolId: string | undefined;
  JobId: string | undefined;
}
export interface DescribeUserImportJobResponse {
  UserImportJob?: UserImportJobType | undefined;
}
export interface DescribeUserPoolRequest {
  UserPoolId: string | undefined;
}
export interface DescribeUserPoolResponse {
  UserPool?: UserPoolType | undefined;
}
export interface DescribeUserPoolClientRequest {
  UserPoolId: string | undefined;
  ClientId: string | undefined;
}
export interface DescribeUserPoolClientResponse {
  UserPoolClient?: UserPoolClientType | undefined;
}
export interface DescribeUserPoolDomainRequest {
  Domain: string | undefined;
}
export declare const DomainStatusType: {
  readonly ACTIVE: "ACTIVE";
  readonly CREATING: "CREATING";
  readonly DELETING: "DELETING";
  readonly FAILED: "FAILED";
  readonly UPDATING: "UPDATING";
};
export type DomainStatusType =
  (typeof DomainStatusType)[keyof typeof DomainStatusType];
export interface DomainDescriptionType {
  UserPoolId?: string | undefined;
  AWSAccountId?: string | undefined;
  Domain?: string | undefined;
  S3Bucket?: string | undefined;
  CloudFrontDistribution?: string | undefined;
  Version?: string | undefined;
  Status?: DomainStatusType | undefined;
  CustomDomainConfig?: CustomDomainConfigType | undefined;
  ManagedLoginVersion?: number | undefined;
}
export interface DescribeUserPoolDomainResponse {
  DomainDescription?: DomainDescriptionType | undefined;
}
export interface ForgetDeviceRequest {
  AccessToken?: string | undefined;
  DeviceKey: string | undefined;
}
export interface ForgotPasswordRequest {
  ClientId: string | undefined;
  SecretHash?: string | undefined;
  UserContextData?: UserContextDataType | undefined;
  Username: string | undefined;
  AnalyticsMetadata?: AnalyticsMetadataType | undefined;
  ClientMetadata?: Record<string, string> | undefined;
}
export interface CodeDeliveryDetailsType {
  Destination?: string | undefined;
  DeliveryMedium?: DeliveryMediumType | undefined;
  AttributeName?: string | undefined;
}
export interface ForgotPasswordResponse {
  CodeDeliveryDetails?: CodeDeliveryDetailsType | undefined;
}
export interface GetCSVHeaderRequest {
  UserPoolId: string | undefined;
}
export interface GetCSVHeaderResponse {
  UserPoolId?: string | undefined;
  CSVHeader?: string[] | undefined;
}
export interface GetDeviceRequest {
  DeviceKey: string | undefined;
  AccessToken?: string | undefined;
}
export interface GetDeviceResponse {
  Device: DeviceType | undefined;
}
export interface GetGroupRequest {
  GroupName: string | undefined;
  UserPoolId: string | undefined;
}
export interface GetGroupResponse {
  Group?: GroupType | undefined;
}
export interface GetIdentityProviderByIdentifierRequest {
  UserPoolId: string | undefined;
  IdpIdentifier: string | undefined;
}
export interface GetIdentityProviderByIdentifierResponse {
  IdentityProvider: IdentityProviderType | undefined;
}
export interface GetLogDeliveryConfigurationRequest {
  UserPoolId: string | undefined;
}
export interface CloudWatchLogsConfigurationType {
  LogGroupArn?: string | undefined;
}
export declare const EventSourceName: {
  readonly USER_AUTH_EVENTS: "userAuthEvents";
  readonly USER_NOTIFICATION: "userNotification";
};
export type EventSourceName =
  (typeof EventSourceName)[keyof typeof EventSourceName];
export interface FirehoseConfigurationType {
  StreamArn?: string | undefined;
}
export declare const LogLevel: {
  readonly ERROR: "ERROR";
  readonly INFO: "INFO";
};
export type LogLevel = (typeof LogLevel)[keyof typeof LogLevel];
export interface S3ConfigurationType {
  BucketArn?: string | undefined;
}
export interface LogConfigurationType {
  LogLevel: LogLevel | undefined;
  EventSource: EventSourceName | undefined;
  CloudWatchLogsConfiguration?: CloudWatchLogsConfigurationType | undefined;
  S3Configuration?: S3ConfigurationType | undefined;
  FirehoseConfiguration?: FirehoseConfigurationType | undefined;
}
export interface LogDeliveryConfigurationType {
  UserPoolId: string | undefined;
  LogConfigurations: LogConfigurationType[] | undefined;
}
export interface GetLogDeliveryConfigurationResponse {
  LogDeliveryConfiguration?: LogDeliveryConfigurationType | undefined;
}
export interface GetSigningCertificateRequest {
  UserPoolId: string | undefined;
}
export interface GetSigningCertificateResponse {
  Certificate?: string | undefined;
}
export interface GetTokensFromRefreshTokenRequest {
  RefreshToken: string | undefined;
  ClientId: string | undefined;
  ClientSecret?: string | undefined;
  DeviceKey?: string | undefined;
  ClientMetadata?: Record<string, string> | undefined;
}
export interface GetTokensFromRefreshTokenResponse {
  AuthenticationResult?: AuthenticationResultType | undefined;
}
export declare class RefreshTokenReuseException extends __BaseException {
  readonly name: "RefreshTokenReuseException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<RefreshTokenReuseException, __BaseException>
  );
}
export interface GetUICustomizationRequest {
  UserPoolId: string | undefined;
  ClientId?: string | undefined;
}
export interface UICustomizationType {
  UserPoolId?: string | undefined;
  ClientId?: string | undefined;
  ImageUrl?: string | undefined;
  CSS?: string | undefined;
  CSSVersion?: string | undefined;
  LastModifiedDate?: Date | undefined;
  CreationDate?: Date | undefined;
}
export interface GetUICustomizationResponse {
  UICustomization: UICustomizationType | undefined;
}
export interface GetUserRequest {
  AccessToken: string | undefined;
}
export interface GetUserResponse {
  Username: string | undefined;
  UserAttributes: AttributeType[] | undefined;
  MFAOptions?: MFAOptionType[] | undefined;
  PreferredMfaSetting?: string | undefined;
  UserMFASettingList?: string[] | undefined;
}
export interface GetUserAttributeVerificationCodeRequest {
  AccessToken: string | undefined;
  AttributeName: string | undefined;
  ClientMetadata?: Record<string, string> | undefined;
}
export interface GetUserAttributeVerificationCodeResponse {
  CodeDeliveryDetails?: CodeDeliveryDetailsType | undefined;
}
export declare const AdminAddUserToGroupRequestFilterSensitiveLog: (
  obj: AdminAddUserToGroupRequest
) => any;
export declare const AdminConfirmSignUpRequestFilterSensitiveLog: (
  obj: AdminConfirmSignUpRequest
) => any;
export declare const AttributeTypeFilterSensitiveLog: (
  obj: AttributeType
) => any;
export declare const AdminCreateUserRequestFilterSensitiveLog: (
  obj: AdminCreateUserRequest
) => any;
export declare const UserTypeFilterSensitiveLog: (obj: UserType) => any;
export declare const AdminCreateUserResponseFilterSensitiveLog: (
  obj: AdminCreateUserResponse
) => any;
export declare const AdminDeleteUserRequestFilterSensitiveLog: (
  obj: AdminDeleteUserRequest
) => any;
export declare const AdminDeleteUserAttributesRequestFilterSensitiveLog: (
  obj: AdminDeleteUserAttributesRequest
) => any;
export declare const AdminDisableUserRequestFilterSensitiveLog: (
  obj: AdminDisableUserRequest
) => any;
export declare const AdminEnableUserRequestFilterSensitiveLog: (
  obj: AdminEnableUserRequest
) => any;
export declare const AdminForgetDeviceRequestFilterSensitiveLog: (
  obj: AdminForgetDeviceRequest
) => any;
export declare const AdminGetDeviceRequestFilterSensitiveLog: (
  obj: AdminGetDeviceRequest
) => any;
export declare const DeviceTypeFilterSensitiveLog: (obj: DeviceType) => any;
export declare const AdminGetDeviceResponseFilterSensitiveLog: (
  obj: AdminGetDeviceResponse
) => any;
export declare const AdminGetUserRequestFilterSensitiveLog: (
  obj: AdminGetUserRequest
) => any;
export declare const AdminGetUserResponseFilterSensitiveLog: (
  obj: AdminGetUserResponse
) => any;
export declare const AdminInitiateAuthRequestFilterSensitiveLog: (
  obj: AdminInitiateAuthRequest
) => any;
export declare const AuthenticationResultTypeFilterSensitiveLog: (
  obj: AuthenticationResultType
) => any;
export declare const AdminInitiateAuthResponseFilterSensitiveLog: (
  obj: AdminInitiateAuthResponse
) => any;
export declare const AdminListDevicesRequestFilterSensitiveLog: (
  obj: AdminListDevicesRequest
) => any;
export declare const AdminListDevicesResponseFilterSensitiveLog: (
  obj: AdminListDevicesResponse
) => any;
export declare const AdminListGroupsForUserRequestFilterSensitiveLog: (
  obj: AdminListGroupsForUserRequest
) => any;
export declare const AdminListUserAuthEventsRequestFilterSensitiveLog: (
  obj: AdminListUserAuthEventsRequest
) => any;
export declare const AdminRemoveUserFromGroupRequestFilterSensitiveLog: (
  obj: AdminRemoveUserFromGroupRequest
) => any;
export declare const AdminResetUserPasswordRequestFilterSensitiveLog: (
  obj: AdminResetUserPasswordRequest
) => any;
export declare const AdminRespondToAuthChallengeRequestFilterSensitiveLog: (
  obj: AdminRespondToAuthChallengeRequest
) => any;
export declare const AdminRespondToAuthChallengeResponseFilterSensitiveLog: (
  obj: AdminRespondToAuthChallengeResponse
) => any;
export declare const AdminSetUserMFAPreferenceRequestFilterSensitiveLog: (
  obj: AdminSetUserMFAPreferenceRequest
) => any;
export declare const AdminSetUserPasswordRequestFilterSensitiveLog: (
  obj: AdminSetUserPasswordRequest
) => any;
export declare const AdminSetUserSettingsRequestFilterSensitiveLog: (
  obj: AdminSetUserSettingsRequest
) => any;
export declare const AdminUpdateAuthEventFeedbackRequestFilterSensitiveLog: (
  obj: AdminUpdateAuthEventFeedbackRequest
) => any;
export declare const AdminUpdateDeviceStatusRequestFilterSensitiveLog: (
  obj: AdminUpdateDeviceStatusRequest
) => any;
export declare const AdminUpdateUserAttributesRequestFilterSensitiveLog: (
  obj: AdminUpdateUserAttributesRequest
) => any;
export declare const AdminUserGlobalSignOutRequestFilterSensitiveLog: (
  obj: AdminUserGlobalSignOutRequest
) => any;
export declare const AssociateSoftwareTokenRequestFilterSensitiveLog: (
  obj: AssociateSoftwareTokenRequest
) => any;
export declare const AssociateSoftwareTokenResponseFilterSensitiveLog: (
  obj: AssociateSoftwareTokenResponse
) => any;
export declare const ChangePasswordRequestFilterSensitiveLog: (
  obj: ChangePasswordRequest
) => any;
export declare const CompleteWebAuthnRegistrationRequestFilterSensitiveLog: (
  obj: CompleteWebAuthnRegistrationRequest
) => any;
export declare const ConfirmDeviceRequestFilterSensitiveLog: (
  obj: ConfirmDeviceRequest
) => any;
export declare const UserContextDataTypeFilterSensitiveLog: (
  obj: UserContextDataType
) => any;
export declare const ConfirmForgotPasswordRequestFilterSensitiveLog: (
  obj: ConfirmForgotPasswordRequest
) => any;
export declare const ConfirmSignUpRequestFilterSensitiveLog: (
  obj: ConfirmSignUpRequest
) => any;
export declare const ConfirmSignUpResponseFilterSensitiveLog: (
  obj: ConfirmSignUpResponse
) => any;
export declare const CreateManagedLoginBrandingRequestFilterSensitiveLog: (
  obj: CreateManagedLoginBrandingRequest
) => any;
export declare const UserPoolClientTypeFilterSensitiveLog: (
  obj: UserPoolClientType
) => any;
export declare const CreateUserPoolClientResponseFilterSensitiveLog: (
  obj: CreateUserPoolClientResponse
) => any;
export declare const DeleteUserRequestFilterSensitiveLog: (
  obj: DeleteUserRequest
) => any;
export declare const DeleteUserAttributesRequestFilterSensitiveLog: (
  obj: DeleteUserAttributesRequest
) => any;
export declare const DeleteUserPoolClientRequestFilterSensitiveLog: (
  obj: DeleteUserPoolClientRequest
) => any;
export declare const DeleteWebAuthnCredentialRequestFilterSensitiveLog: (
  obj: DeleteWebAuthnCredentialRequest
) => any;
export declare const DescribeManagedLoginBrandingByClientRequestFilterSensitiveLog: (
  obj: DescribeManagedLoginBrandingByClientRequest
) => any;
export declare const DescribeRiskConfigurationRequestFilterSensitiveLog: (
  obj: DescribeRiskConfigurationRequest
) => any;
export declare const RiskConfigurationTypeFilterSensitiveLog: (
  obj: RiskConfigurationType
) => any;
export declare const DescribeRiskConfigurationResponseFilterSensitiveLog: (
  obj: DescribeRiskConfigurationResponse
) => any;
export declare const DescribeUserPoolClientRequestFilterSensitiveLog: (
  obj: DescribeUserPoolClientRequest
) => any;
export declare const DescribeUserPoolClientResponseFilterSensitiveLog: (
  obj: DescribeUserPoolClientResponse
) => any;
export declare const ForgetDeviceRequestFilterSensitiveLog: (
  obj: ForgetDeviceRequest
) => any;
export declare const ForgotPasswordRequestFilterSensitiveLog: (
  obj: ForgotPasswordRequest
) => any;
export declare const GetDeviceRequestFilterSensitiveLog: (
  obj: GetDeviceRequest
) => any;
export declare const GetDeviceResponseFilterSensitiveLog: (
  obj: GetDeviceResponse
) => any;
export declare const GetTokensFromRefreshTokenRequestFilterSensitiveLog: (
  obj: GetTokensFromRefreshTokenRequest
) => any;
export declare const GetTokensFromRefreshTokenResponseFilterSensitiveLog: (
  obj: GetTokensFromRefreshTokenResponse
) => any;
export declare const GetUICustomizationRequestFilterSensitiveLog: (
  obj: GetUICustomizationRequest
) => any;
export declare const UICustomizationTypeFilterSensitiveLog: (
  obj: UICustomizationType
) => any;
export declare const GetUICustomizationResponseFilterSensitiveLog: (
  obj: GetUICustomizationResponse
) => any;
export declare const GetUserRequestFilterSensitiveLog: (
  obj: GetUserRequest
) => any;
export declare const GetUserResponseFilterSensitiveLog: (
  obj: GetUserResponse
) => any;
export declare const GetUserAttributeVerificationCodeRequestFilterSensitiveLog: (
  obj: GetUserAttributeVerificationCodeRequest
) => any;
