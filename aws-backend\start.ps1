# GameFlex AWS Backend Startup Script (PowerShell)
# This script starts the AWS backend using LocalStack Pro on Windows

param(
    [switch]$Force,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Load environment variables from .env file
function Load-EnvFile {
    if (Test-Path ".env") {
        Write-Status "Loading environment variables from .env file..."
        Get-Content ".env" | ForEach-Object {
            if ($_ -match "^\s*([^#][^=]*)\s*=\s*(.*)\s*$") {
                $name = $matches[1].Trim()
                $value = $matches[2].Trim()
                # Remove quotes if present
                $value = $value -replace '^"(.*)"$', '$1'
                $value = $value -replace "^'(.*)'$", '$1'
                [Environment]::SetEnvironmentVariable($name, $value, "Process")
                if ($Verbose) {
                    Write-Host "  Set $name" -ForegroundColor Gray
                }
            }
        }
    }
    else {
        Write-Warning ".env file not found. Using default values."
    }
}

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[GAMEFLEX] $Message" -ForegroundColor Blue
}

# Check if Docker Desktop is running
function Test-Docker {
    try {
        docker info | Out-Null
        Write-Status "Docker Desktop is running"
        return $true
    }
    catch {
        Write-Error "Docker Desktop is not running. Please start Docker Desktop and try again."
        return $false
    }
}

# Check if required ports are available
function Test-Ports {
    $ports = @(4566, 45660)  # Only LocalStack ports needed
    $portsInUse = @()

    foreach ($port in $ports) {
        $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        if ($connection) {
            $portsInUse += $port
            Write-Warning "Port $port is already in use"
        }
    }

    if ($portsInUse.Count -gt 0 -and -not $Force) {
        $response = Read-Host "Do you want to continue anyway? (y/N)"
        if ($response -notmatch "^[Yy]$") {
            Write-Error "Startup cancelled"
            return $false
        }
    }

    Write-Status "Port check completed"
    return $true
}

# Create necessary directories
function New-Directories {
    Write-Status "Creating necessary directories..."
    
    $directories = @(
        "init",
        "lambda-functions",
        "cloudformation", 
        "database\init",
        "logs"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Write-Status "Directories created"
}

# Start Docker services
function Start-Services {
    Write-Header "Starting GameFlex AWS Backend..."
    
    try {
        # Pull latest images
        Write-Status "Pulling latest Docker images..."
        docker compose pull
        
        # Start services
        Write-Status "Starting services..."
        docker compose up -d
        
        # Wait for services to be healthy
        Write-Status "Waiting for services to be ready..."
        
        # Wait for LocalStack
        Write-Status "Waiting for LocalStack to be ready..."
        $timeout = 120
        $counter = 0
        
        do {
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:45660/_localstack/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    Write-Status "LocalStack is ready"
                    break
                }
            }
            catch {
                # Continue waiting
            }
            
            Start-Sleep -Seconds 2
            $counter += 2
            
            if ($counter -ge $timeout) {
                Write-Error "LocalStack failed to start within $timeout seconds"
                docker compose logs localstack
                throw "LocalStack startup timeout"
            }
        } while ($true)
        
        # Wait for DynamoDB to be available through LocalStack
        Write-Status "Waiting for LocalStack DynamoDB to be ready..."
        Start-Sleep -Seconds 5  # Give LocalStack time to initialize DynamoDB
        
        return $true
    }
    catch {
        Write-Host "[ERROR] Failed to start services: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Initialize AWS services
function Initialize-AwsServices {
    Write-Status "Initializing AWS services..."

    if (Test-Path "init\init-aws-services.ps1") {
        Write-Status "Running AWS services initialization..."
        & ".\init\init-aws-services.ps1"
    }
    else {
        Write-Warning "AWS services initialization script not found"
        Write-Warning "You may need to run the initialization manually"
    }
}

# Clean up any existing resources that might conflict
function Cleanup-ExistingResources {
    Write-Status "Cleaning up any conflicting resources..."

    # Set AWS CLI environment for LocalStack
    $env:AWS_ACCESS_KEY_ID = "test"
    $env:AWS_SECRET_ACCESS_KEY = "test"
    $env:AWS_DEFAULT_REGION = "us-east-1"
    $ENDPOINT_URL = "http://localhost:45660"

    try {
        # Check for existing IAM roles and clean them up
        $roles = aws --endpoint-url=$ENDPOINT_URL iam list-roles --query "Roles[?contains(RoleName, 'gameflex')].RoleName" --output text 2>$null
        if ($roles) {
            foreach ($role in $roles.Split("`t")) {
                if ($role.Trim()) {
                    Write-Status "Cleaning up existing role: $role"
                    # Detach managed policies
                    $attachedPolicies = aws --endpoint-url=$ENDPOINT_URL iam list-attached-role-policies --role-name $role --query "AttachedPolicies[].PolicyArn" --output text 2>$null
                    if ($attachedPolicies) {
                        foreach ($policy in $attachedPolicies.Split("`t")) {
                            if ($policy.Trim()) {
                                aws --endpoint-url=$ENDPOINT_URL iam detach-role-policy --role-name $role --policy-arn $policy 2>$null
                            }
                        }
                    }
                    # Delete inline policies
                    $inlinePolicies = aws --endpoint-url=$ENDPOINT_URL iam list-role-policies --role-name $role --query "PolicyNames" --output text 2>$null
                    if ($inlinePolicies) {
                        foreach ($policy in $inlinePolicies.Split("`t")) {
                            if ($policy.Trim()) {
                                aws --endpoint-url=$ENDPOINT_URL iam delete-role-policy --role-name $role --policy-name $policy 2>$null
                            }
                        }
                    }
                    # Delete the role
                    aws --endpoint-url=$ENDPOINT_URL iam delete-role --role-name $role 2>$null
                }
            }
        }
        Write-Status "Resource cleanup completed"
        return $true
    }
    catch {
        Write-Warning "Resource cleanup failed: $_"
        return $true  # Continue even if cleanup fails
    }
}

# Create Lambda function code
function Create-LambdaCode {
    $lambdaCode = @'
exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const body = JSON.parse(event.body || '{}');
    const path = event.path || event.rawPath || '';
    const method = event.httpMethod || event.requestContext?.http?.method || 'GET';

    console.log('Path:', path, 'Method:', method);

    // Handle authentication endpoints
    if (path.includes('signin') && method === 'POST') {
        return {
            statusCode: 200,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({
                success: true,
                user: {
                    id: 'test-user-id',
                    email: body.email || '<EMAIL>',
                    name: 'Test User'
                },
                accessToken: 'test-access-token',
                refreshToken: 'test-refresh-token'
            })
        };
    }

    // Handle posts endpoints
    if (path.includes('posts') && method === 'GET') {
        return {
            statusCode: 200,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({
                posts: [
                    {
                        id: 'test-post-1',
                        content: 'Test post from AWS backend',
                        userId: 'test-user-id',
                        createdAt: new Date().toISOString()
                    }
                ],
                hasMore: false
            })
        };
    }

    if (path.includes('posts') && method === 'POST') {
        return {
            statusCode: 200,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: JSON.stringify({
                id: 'new-post-' + Date.now(),
                content: body.content || 'Default content',
                userId: 'test-user-id',
                createdAt: new Date().toISOString()
            })
        };
    }

    // Handle OPTIONS requests for CORS
    if (method === 'OPTIONS') {
        return {
            statusCode: 200,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type,Authorization',
                'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
            },
            body: ''
        };
    }

    // Default response
    return {
        statusCode: 404,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
            error: 'Endpoint not found',
            path: path,
            method: method
        })
    };
};
'@

    $lambdaCode | Out-File -FilePath "temp-lambda.js" -Encoding UTF8
    Compress-Archive -Path "temp-lambda.js" -DestinationPath "temp-lambda.zip" -Force
}

# Setup minimal API Gateway manually when CloudFormation fails
function Setup-MinimalApiGateway {
    Write-Status "Setting up minimal API Gateway for testing..."

    # Set AWS CLI environment for LocalStack
    $env:AWS_ACCESS_KEY_ID = "test"
    $env:AWS_SECRET_ACCESS_KEY = "test"
    $env:AWS_DEFAULT_REGION = "us-east-1"
    $ENDPOINT_URL = "http://localhost:45660"

    try {
        # Check if API Gateway already exists
        $existingApis = aws --endpoint-url=$ENDPOINT_URL apigateway get-rest-apis --query "items[?name=='gameflex-api-development'].id" --output text 2>$null

        if ($existingApis) {
            Write-Status "API Gateway already exists: $existingApis"
            return $true
        }

        Write-Status "Creating API Gateway..."
        $apiResult = aws --endpoint-url=$ENDPOINT_URL apigateway create-rest-api --name "gameflex-api-development" --description "GameFlex API for development" --output json
        $apiGatewayId = ($apiResult | ConvertFrom-Json).id
        $rootResourceId = ($apiResult | ConvertFrom-Json).rootResourceId

        Write-Status "Created API Gateway: $apiGatewayId"

        # Ensure IAM role exists for Lambda functions
        $roleExists = aws --endpoint-url=$ENDPOINT_URL iam get-role --role-name lambda-role 2>$null
        if (-not $roleExists) {
            Write-Status "Creating Lambda IAM role..."
            if (-not (Test-Path "lambda-trust-policy.json")) {
                @'
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
'@ | Out-File -FilePath "lambda-trust-policy.json" -Encoding UTF8
            }
            aws --endpoint-url=$ENDPOINT_URL iam create-role --role-name lambda-role --assume-role-policy-document file://lambda-trust-policy.json | Out-Null
            aws --endpoint-url=$ENDPOINT_URL iam attach-role-policy --role-name lambda-role --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole | Out-Null
        }

        # Deploy Lambda functions using the initialization script
        Write-Status "Deploying Lambda functions with TypeScript implementations..."
        $deployResult = docker exec localstack-main bash -c "cd /opt/code && chmod +x init/04-deploy-lambda-functions.sh && ./init/04-deploy-lambda-functions.sh"

        if ($LASTEXITCODE -ne 0) {
            Write-Host "[WARN] Lambda deployment script failed, falling back to temporary functions" -ForegroundColor Yellow

            # Fallback: Create temporary Lambda functions
            $authExists = aws --endpoint-url=$ENDPOINT_URL lambda get-function --function-name gameflex-auth-development 2>$null
            if (-not $authExists) {
                Write-Status "Creating temporary auth Lambda function..."
                if (-not (Test-Path "temp-lambda.zip")) {
                    Create-LambdaCode
                }
                aws --endpoint-url=$ENDPOINT_URL lambda create-function --function-name gameflex-auth-development --runtime nodejs18.x --role arn:aws:iam::000000000000:role/lambda-role --handler temp-lambda.handler --zip-file fileb://temp-lambda.zip | Out-Null
            }

            $postsExists = aws --endpoint-url=$ENDPOINT_URL lambda get-function --function-name gameflex-posts-development 2>$null
            if (-not $postsExists) {
                Write-Status "Creating temporary posts Lambda function..."
                aws --endpoint-url=$ENDPOINT_URL lambda create-function --function-name gameflex-posts-development --runtime nodejs18.x --role arn:aws:iam::000000000000:role/lambda-role --handler temp-lambda.handler --zip-file fileb://temp-lambda.zip | Out-Null
            }
        }
        else {
            Write-Host "[SUCCESS] Lambda functions deployed with TypeScript implementations" -ForegroundColor Green
        }

        # Add API Gateway permissions for all functions
        aws --endpoint-url=$ENDPOINT_URL lambda add-permission --function-name gameflex-auth-development --statement-id apigateway-invoke --action lambda:InvokeFunction --principal apigateway.amazonaws.com --source-arn "arn:aws:execute-api:us-east-1:000000000000:$apiGatewayId/*/*" 2>$null | Out-Null
        aws --endpoint-url=$ENDPOINT_URL lambda add-permission --function-name gameflex-posts-development --statement-id apigateway-invoke --action lambda:InvokeFunction --principal apigateway.amazonaws.com --source-arn "arn:aws:execute-api:us-east-1:000000000000:$apiGatewayId/*/*" 2>$null | Out-Null

        Write-Status "API Gateway setup completed"
        return $true
    }
    catch {
        Write-Warning "Failed to setup minimal API Gateway: $_"
        return $false
    }
}

# Deploy CloudFormation infrastructure directly
function Deploy-Infrastructure {
    Write-Status "Deploying CloudFormation infrastructure..."

    # Set AWS CLI environment for LocalStack
    $env:AWS_ACCESS_KEY_ID = "test"
    $env:AWS_SECRET_ACCESS_KEY = "test"
    $env:AWS_DEFAULT_REGION = "us-east-1"
    $ENDPOINT_URL = "http://localhost:45660"

    try {
        # Check if stack already exists
        $stackExists = $false
        try {
            aws --endpoint-url=$ENDPOINT_URL cloudformation describe-stacks --stack-name gameflex-infrastructure-development | Out-Null
            $stackExists = $true
            Write-Status "Stack already exists, updating..."
        }
        catch {
            Write-Status "Creating new stack..."
        }

        if ($stackExists) {
            # Update existing stack
            aws --endpoint-url=$ENDPOINT_URL cloudformation update-stack --stack-name gameflex-infrastructure-development --template-body file://cloudformation/gameflex-infrastructure.yaml --parameters file://cloudformation/parameters/development.json --capabilities CAPABILITY_IAM
            if ($LASTEXITCODE -eq 0) {
                Write-Status "Waiting for stack update to complete..."
                aws --endpoint-url=$ENDPOINT_URL cloudformation wait stack-update-complete --stack-name gameflex-infrastructure-development
            }
        }
        else {
            # Create new stack
            aws --endpoint-url=$ENDPOINT_URL cloudformation create-stack --stack-name gameflex-infrastructure-development --template-body file://cloudformation/gameflex-infrastructure.yaml --parameters file://cloudformation/parameters/development.json --capabilities CAPABILITY_IAM
            if ($LASTEXITCODE -eq 0) {
                Write-Status "Waiting for stack creation to complete..."
                aws --endpoint-url=$ENDPOINT_URL cloudformation wait stack-create-complete --stack-name gameflex-infrastructure-development
            }
        }

        if ($LASTEXITCODE -eq 0) {
            Write-Status "Infrastructure deployment completed successfully"
            return $true
        }
        else {
            Write-Warning "Infrastructure deployment failed, but continuing..."
            return $false
        }
    }
    catch {
        Write-Warning "Infrastructure deployment failed: $_"
        return $false
    }
}

# Display service information
function Show-ServiceInfo {
    Write-Header "GameFlex AWS Backend (LocalStack Pro) is now running!"
    Write-Host ""

    Write-Status "Service URLs:"
    Write-Host "  LocalStack Dashboard: http://localhost:45660/_localstack/health" -ForegroundColor Cyan
    Write-Host "  LocalStack Pro Dashboard: http://localhost:4566/_localstack/health" -ForegroundColor Cyan
    Write-Host "  LocalStack HTTPS Gateway: https://localhost:443" -ForegroundColor Cyan
    Write-Host "  DynamoDB (via LocalStack): http://localhost:4566" -ForegroundColor Cyan
    Write-Host "  S3 Console: http://localhost:45660/_aws/s3" -ForegroundColor Cyan
    Write-Host "  Cognito Console: http://localhost:45660/_aws/cognito" -ForegroundColor Cyan
    Write-Host ""

    Write-Status "Development Credentials:"
    Write-Host "  Developer: <EMAIL> / DevPassword123!" -ForegroundColor Cyan
    Write-Host "  Admin: <EMAIL> / AdminPassword123!" -ForegroundColor Cyan
    Write-Host ""

    Write-Status "Useful Commands:"
    Write-Host "  Check status: docker compose ps" -ForegroundColor Cyan
    Write-Host "  View logs: docker compose logs -f" -ForegroundColor Cyan
    Write-Host "  Stop services: .\stop.ps1" -ForegroundColor Cyan
    Write-Host "  Restart: .\stop.ps1; .\start.ps1" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Warning "This is a development environment. Do not use in production!"
}

# Main execution
function Main {
    Write-Header "GameFlex AWS Backend Startup"
    Write-Host ""

    # Load environment variables from .env file
    Load-EnvFile

    if (-not (Test-Docker)) {
        exit 1
    }
    
    if (-not (Test-Ports)) {
        exit 1
    }
    
    New-Directories
    
    if (-not (Start-Services)) {
        exit 1
    }

    Initialize-AwsServices

    Cleanup-ExistingResources

    if (-not (Deploy-Infrastructure)) {
        Write-Warning "Infrastructure deployment failed, creating minimal API Gateway manually..."
        Setup-MinimalApiGateway
    }

    Show-ServiceInfo
    
    Write-Status "Startup completed successfully!"
}

# Run main function
try {
    Main
}
catch {
    Write-Host "[ERROR] Startup failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
