import { ExceptionOptionType as __ExceptionOptionType } from "@smithy/smithy-client";
import { DocumentType as __DocumentType } from "@smithy/types";
import { CognitoIdentityProviderServiceException as __BaseException } from "./CognitoIdentityProviderServiceException";
import {
  AccountRecoverySettingType,
  AccountTakeoverRiskConfigurationType,
  AdminCreateUserConfigType,
  AnalyticsConfigurationType,
  AnalyticsMetadataType,
  AssetType,
  AttributeType,
  AuthenticationResultType,
  AuthFactorType,
  AuthFlowType,
  ChallengeNameType,
  CodeDeliveryDetailsType,
  CompromisedCredentialsRiskConfigurationType,
  CustomDomainConfigType,
  DeletionProtectionType,
  DeviceConfigurationType,
  DeviceRememberedStatusType,
  DeviceType,
  EmailConfigurationType,
  EmailMfaSettingsType,
  ExplicitAuthFlowsType,
  FeedbackValueType,
  GroupType,
  IdentityProviderType,
  IdentityProviderTypeType,
  LambdaConfigType,
  LogConfigurationType,
  LogDeliveryConfigurationType,
  ManagedLoginBrandingType,
  MFAOptionType,
  OAuthFlowType,
  PreventUserExistenceErrorTypes,
  RefreshTokenRotationType,
  ResourceServerScopeType,
  ResourceServerType,
  RiskConfigurationType,
  RiskExceptionConfigurationType,
  SmsConfigurationType,
  SMSMfaSettingsType,
  SoftwareTokenMfaSettingsType,
  StatusType,
  TokenValidityUnitsType,
  UICustomizationType,
  UserAttributeUpdateSettingsType,
  UserContextDataType,
  UserImportJobType,
  UserPoolAddOnsType,
  UserPoolClientType,
  UserPoolMfaType,
  UserPoolPolicyType,
  UserPoolTierType,
  UserType,
  VerificationMessageTemplateType,
  VerifiedAttributeType,
} from "./models_0";
export interface GetUserAuthFactorsRequest {
  AccessToken: string | undefined;
}
export interface GetUserAuthFactorsResponse {
  Username: string | undefined;
  PreferredMfaSetting?: string | undefined;
  UserMFASettingList?: string[] | undefined;
  ConfiguredUserAuthFactors?: AuthFactorType[] | undefined;
}
export interface GetUserPoolMfaConfigRequest {
  UserPoolId: string | undefined;
}
export interface EmailMfaConfigType {
  Message?: string | undefined;
  Subject?: string | undefined;
}
export interface SmsMfaConfigType {
  SmsAuthenticationMessage?: string | undefined;
  SmsConfiguration?: SmsConfigurationType | undefined;
}
export interface SoftwareTokenMfaConfigType {
  Enabled?: boolean | undefined;
}
export declare const UserVerificationType: {
  readonly PREFERRED: "preferred";
  readonly REQUIRED: "required";
};
export type UserVerificationType =
  (typeof UserVerificationType)[keyof typeof UserVerificationType];
export interface WebAuthnConfigurationType {
  RelyingPartyId?: string | undefined;
  UserVerification?: UserVerificationType | undefined;
}
export interface GetUserPoolMfaConfigResponse {
  SmsMfaConfiguration?: SmsMfaConfigType | undefined;
  SoftwareTokenMfaConfiguration?: SoftwareTokenMfaConfigType | undefined;
  EmailMfaConfiguration?: EmailMfaConfigType | undefined;
  MfaConfiguration?: UserPoolMfaType | undefined;
  WebAuthnConfiguration?: WebAuthnConfigurationType | undefined;
}
export interface GlobalSignOutRequest {
  AccessToken: string | undefined;
}
export interface GlobalSignOutResponse {}
export interface InitiateAuthRequest {
  AuthFlow: AuthFlowType | undefined;
  AuthParameters?: Record<string, string> | undefined;
  ClientMetadata?: Record<string, string> | undefined;
  ClientId: string | undefined;
  AnalyticsMetadata?: AnalyticsMetadataType | undefined;
  UserContextData?: UserContextDataType | undefined;
  Session?: string | undefined;
}
export interface InitiateAuthResponse {
  ChallengeName?: ChallengeNameType | undefined;
  Session?: string | undefined;
  ChallengeParameters?: Record<string, string> | undefined;
  AuthenticationResult?: AuthenticationResultType | undefined;
  AvailableChallenges?: ChallengeNameType[] | undefined;
}
export interface ListDevicesRequest {
  AccessToken: string | undefined;
  Limit?: number | undefined;
  PaginationToken?: string | undefined;
}
export interface ListDevicesResponse {
  Devices?: DeviceType[] | undefined;
  PaginationToken?: string | undefined;
}
export interface ListGroupsRequest {
  UserPoolId: string | undefined;
  Limit?: number | undefined;
  NextToken?: string | undefined;
}
export interface ListGroupsResponse {
  Groups?: GroupType[] | undefined;
  NextToken?: string | undefined;
}
export interface ListIdentityProvidersRequest {
  UserPoolId: string | undefined;
  MaxResults?: number | undefined;
  NextToken?: string | undefined;
}
export interface ProviderDescription {
  ProviderName?: string | undefined;
  ProviderType?: IdentityProviderTypeType | undefined;
  LastModifiedDate?: Date | undefined;
  CreationDate?: Date | undefined;
}
export interface ListIdentityProvidersResponse {
  Providers: ProviderDescription[] | undefined;
  NextToken?: string | undefined;
}
export interface ListResourceServersRequest {
  UserPoolId: string | undefined;
  MaxResults?: number | undefined;
  NextToken?: string | undefined;
}
export interface ListResourceServersResponse {
  ResourceServers: ResourceServerType[] | undefined;
  NextToken?: string | undefined;
}
export interface ListTagsForResourceRequest {
  ResourceArn: string | undefined;
}
export interface ListTagsForResourceResponse {
  Tags?: Record<string, string> | undefined;
}
export interface ListUserImportJobsRequest {
  UserPoolId: string | undefined;
  MaxResults: number | undefined;
  PaginationToken?: string | undefined;
}
export interface ListUserImportJobsResponse {
  UserImportJobs?: UserImportJobType[] | undefined;
  PaginationToken?: string | undefined;
}
export interface ListUserPoolClientsRequest {
  UserPoolId: string | undefined;
  MaxResults?: number | undefined;
  NextToken?: string | undefined;
}
export interface UserPoolClientDescription {
  ClientId?: string | undefined;
  UserPoolId?: string | undefined;
  ClientName?: string | undefined;
}
export interface ListUserPoolClientsResponse {
  UserPoolClients?: UserPoolClientDescription[] | undefined;
  NextToken?: string | undefined;
}
export interface ListUserPoolsRequest {
  NextToken?: string | undefined;
  MaxResults: number | undefined;
}
export interface UserPoolDescriptionType {
  Id?: string | undefined;
  Name?: string | undefined;
  LambdaConfig?: LambdaConfigType | undefined;
  Status?: StatusType | undefined;
  LastModifiedDate?: Date | undefined;
  CreationDate?: Date | undefined;
}
export interface ListUserPoolsResponse {
  UserPools?: UserPoolDescriptionType[] | undefined;
  NextToken?: string | undefined;
}
export interface ListUsersRequest {
  UserPoolId: string | undefined;
  AttributesToGet?: string[] | undefined;
  Limit?: number | undefined;
  PaginationToken?: string | undefined;
  Filter?: string | undefined;
}
export interface ListUsersResponse {
  Users?: UserType[] | undefined;
  PaginationToken?: string | undefined;
}
export interface ListUsersInGroupRequest {
  UserPoolId: string | undefined;
  GroupName: string | undefined;
  Limit?: number | undefined;
  NextToken?: string | undefined;
}
export interface ListUsersInGroupResponse {
  Users?: UserType[] | undefined;
  NextToken?: string | undefined;
}
export interface ListWebAuthnCredentialsRequest {
  AccessToken: string | undefined;
  NextToken?: string | undefined;
  MaxResults?: number | undefined;
}
export interface WebAuthnCredentialDescription {
  CredentialId: string | undefined;
  FriendlyCredentialName: string | undefined;
  RelyingPartyId: string | undefined;
  AuthenticatorAttachment?: string | undefined;
  AuthenticatorTransports: string[] | undefined;
  CreatedAt: Date | undefined;
}
export interface ListWebAuthnCredentialsResponse {
  Credentials: WebAuthnCredentialDescription[] | undefined;
  NextToken?: string | undefined;
}
export interface ResendConfirmationCodeRequest {
  ClientId: string | undefined;
  SecretHash?: string | undefined;
  UserContextData?: UserContextDataType | undefined;
  Username: string | undefined;
  AnalyticsMetadata?: AnalyticsMetadataType | undefined;
  ClientMetadata?: Record<string, string> | undefined;
}
export interface ResendConfirmationCodeResponse {
  CodeDeliveryDetails?: CodeDeliveryDetailsType | undefined;
}
export interface RespondToAuthChallengeRequest {
  ClientId: string | undefined;
  ChallengeName: ChallengeNameType | undefined;
  Session?: string | undefined;
  ChallengeResponses?: Record<string, string> | undefined;
  AnalyticsMetadata?: AnalyticsMetadataType | undefined;
  UserContextData?: UserContextDataType | undefined;
  ClientMetadata?: Record<string, string> | undefined;
}
export interface RespondToAuthChallengeResponse {
  ChallengeName?: ChallengeNameType | undefined;
  Session?: string | undefined;
  ChallengeParameters?: Record<string, string> | undefined;
  AuthenticationResult?: AuthenticationResultType | undefined;
}
export interface RevokeTokenRequest {
  Token: string | undefined;
  ClientId: string | undefined;
  ClientSecret?: string | undefined;
}
export interface RevokeTokenResponse {}
export declare class UnauthorizedException extends __BaseException {
  readonly name: "UnauthorizedException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<UnauthorizedException, __BaseException>
  );
}
export declare class UnsupportedTokenTypeException extends __BaseException {
  readonly name: "UnsupportedTokenTypeException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<UnsupportedTokenTypeException, __BaseException>
  );
}
export interface SetLogDeliveryConfigurationRequest {
  UserPoolId: string | undefined;
  LogConfigurations: LogConfigurationType[] | undefined;
}
export interface SetLogDeliveryConfigurationResponse {
  LogDeliveryConfiguration?: LogDeliveryConfigurationType | undefined;
}
export interface SetRiskConfigurationRequest {
  UserPoolId: string | undefined;
  ClientId?: string | undefined;
  CompromisedCredentialsRiskConfiguration?:
    | CompromisedCredentialsRiskConfigurationType
    | undefined;
  AccountTakeoverRiskConfiguration?:
    | AccountTakeoverRiskConfigurationType
    | undefined;
  RiskExceptionConfiguration?: RiskExceptionConfigurationType | undefined;
}
export interface SetRiskConfigurationResponse {
  RiskConfiguration: RiskConfigurationType | undefined;
}
export interface SetUICustomizationRequest {
  UserPoolId: string | undefined;
  ClientId?: string | undefined;
  CSS?: string | undefined;
  ImageFile?: Uint8Array | undefined;
}
export interface SetUICustomizationResponse {
  UICustomization: UICustomizationType | undefined;
}
export interface SetUserMFAPreferenceRequest {
  SMSMfaSettings?: SMSMfaSettingsType | undefined;
  SoftwareTokenMfaSettings?: SoftwareTokenMfaSettingsType | undefined;
  EmailMfaSettings?: EmailMfaSettingsType | undefined;
  AccessToken: string | undefined;
}
export interface SetUserMFAPreferenceResponse {}
export interface SetUserPoolMfaConfigRequest {
  UserPoolId: string | undefined;
  SmsMfaConfiguration?: SmsMfaConfigType | undefined;
  SoftwareTokenMfaConfiguration?: SoftwareTokenMfaConfigType | undefined;
  EmailMfaConfiguration?: EmailMfaConfigType | undefined;
  MfaConfiguration?: UserPoolMfaType | undefined;
  WebAuthnConfiguration?: WebAuthnConfigurationType | undefined;
}
export interface SetUserPoolMfaConfigResponse {
  SmsMfaConfiguration?: SmsMfaConfigType | undefined;
  SoftwareTokenMfaConfiguration?: SoftwareTokenMfaConfigType | undefined;
  EmailMfaConfiguration?: EmailMfaConfigType | undefined;
  MfaConfiguration?: UserPoolMfaType | undefined;
  WebAuthnConfiguration?: WebAuthnConfigurationType | undefined;
}
export interface SetUserSettingsRequest {
  AccessToken: string | undefined;
  MFAOptions: MFAOptionType[] | undefined;
}
export interface SetUserSettingsResponse {}
export interface SignUpRequest {
  ClientId: string | undefined;
  SecretHash?: string | undefined;
  Username: string | undefined;
  Password?: string | undefined;
  UserAttributes?: AttributeType[] | undefined;
  ValidationData?: AttributeType[] | undefined;
  AnalyticsMetadata?: AnalyticsMetadataType | undefined;
  UserContextData?: UserContextDataType | undefined;
  ClientMetadata?: Record<string, string> | undefined;
}
export interface SignUpResponse {
  UserConfirmed: boolean | undefined;
  CodeDeliveryDetails?: CodeDeliveryDetailsType | undefined;
  UserSub: string | undefined;
  Session?: string | undefined;
}
export interface StartUserImportJobRequest {
  UserPoolId: string | undefined;
  JobId: string | undefined;
}
export interface StartUserImportJobResponse {
  UserImportJob?: UserImportJobType | undefined;
}
export interface StartWebAuthnRegistrationRequest {
  AccessToken: string | undefined;
}
export interface StartWebAuthnRegistrationResponse {
  CredentialCreationOptions: __DocumentType | undefined;
}
export declare class WebAuthnConfigurationMissingException extends __BaseException {
  readonly name: "WebAuthnConfigurationMissingException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      WebAuthnConfigurationMissingException,
      __BaseException
    >
  );
}
export interface StopUserImportJobRequest {
  UserPoolId: string | undefined;
  JobId: string | undefined;
}
export interface StopUserImportJobResponse {
  UserImportJob?: UserImportJobType | undefined;
}
export interface TagResourceRequest {
  ResourceArn: string | undefined;
  Tags: Record<string, string> | undefined;
}
export interface TagResourceResponse {}
export interface UntagResourceRequest {
  ResourceArn: string | undefined;
  TagKeys: string[] | undefined;
}
export interface UntagResourceResponse {}
export interface UpdateAuthEventFeedbackRequest {
  UserPoolId: string | undefined;
  Username: string | undefined;
  EventId: string | undefined;
  FeedbackToken: string | undefined;
  FeedbackValue: FeedbackValueType | undefined;
}
export interface UpdateAuthEventFeedbackResponse {}
export interface UpdateDeviceStatusRequest {
  AccessToken: string | undefined;
  DeviceKey: string | undefined;
  DeviceRememberedStatus?: DeviceRememberedStatusType | undefined;
}
export interface UpdateDeviceStatusResponse {}
export interface UpdateGroupRequest {
  GroupName: string | undefined;
  UserPoolId: string | undefined;
  Description?: string | undefined;
  RoleArn?: string | undefined;
  Precedence?: number | undefined;
}
export interface UpdateGroupResponse {
  Group?: GroupType | undefined;
}
export interface UpdateIdentityProviderRequest {
  UserPoolId: string | undefined;
  ProviderName: string | undefined;
  ProviderDetails?: Record<string, string> | undefined;
  AttributeMapping?: Record<string, string> | undefined;
  IdpIdentifiers?: string[] | undefined;
}
export interface UpdateIdentityProviderResponse {
  IdentityProvider: IdentityProviderType | undefined;
}
export interface UpdateManagedLoginBrandingRequest {
  UserPoolId?: string | undefined;
  ManagedLoginBrandingId?: string | undefined;
  UseCognitoProvidedValues?: boolean | undefined;
  Settings?: __DocumentType | undefined;
  Assets?: AssetType[] | undefined;
}
export interface UpdateManagedLoginBrandingResponse {
  ManagedLoginBranding?: ManagedLoginBrandingType | undefined;
}
export interface UpdateResourceServerRequest {
  UserPoolId: string | undefined;
  Identifier: string | undefined;
  Name: string | undefined;
  Scopes?: ResourceServerScopeType[] | undefined;
}
export interface UpdateResourceServerResponse {
  ResourceServer: ResourceServerType | undefined;
}
export interface UpdateUserAttributesRequest {
  UserAttributes: AttributeType[] | undefined;
  AccessToken: string | undefined;
  ClientMetadata?: Record<string, string> | undefined;
}
export interface UpdateUserAttributesResponse {
  CodeDeliveryDetailsList?: CodeDeliveryDetailsType[] | undefined;
}
export interface UpdateUserPoolRequest {
  UserPoolId: string | undefined;
  Policies?: UserPoolPolicyType | undefined;
  DeletionProtection?: DeletionProtectionType | undefined;
  LambdaConfig?: LambdaConfigType | undefined;
  AutoVerifiedAttributes?: VerifiedAttributeType[] | undefined;
  SmsVerificationMessage?: string | undefined;
  EmailVerificationMessage?: string | undefined;
  EmailVerificationSubject?: string | undefined;
  VerificationMessageTemplate?: VerificationMessageTemplateType | undefined;
  SmsAuthenticationMessage?: string | undefined;
  UserAttributeUpdateSettings?: UserAttributeUpdateSettingsType | undefined;
  MfaConfiguration?: UserPoolMfaType | undefined;
  DeviceConfiguration?: DeviceConfigurationType | undefined;
  EmailConfiguration?: EmailConfigurationType | undefined;
  SmsConfiguration?: SmsConfigurationType | undefined;
  UserPoolTags?: Record<string, string> | undefined;
  AdminCreateUserConfig?: AdminCreateUserConfigType | undefined;
  UserPoolAddOns?: UserPoolAddOnsType | undefined;
  AccountRecoverySetting?: AccountRecoverySettingType | undefined;
  PoolName?: string | undefined;
  UserPoolTier?: UserPoolTierType | undefined;
}
export interface UpdateUserPoolResponse {}
export interface UpdateUserPoolClientRequest {
  UserPoolId: string | undefined;
  ClientId: string | undefined;
  ClientName?: string | undefined;
  RefreshTokenValidity?: number | undefined;
  AccessTokenValidity?: number | undefined;
  IdTokenValidity?: number | undefined;
  TokenValidityUnits?: TokenValidityUnitsType | undefined;
  ReadAttributes?: string[] | undefined;
  WriteAttributes?: string[] | undefined;
  ExplicitAuthFlows?: ExplicitAuthFlowsType[] | undefined;
  SupportedIdentityProviders?: string[] | undefined;
  CallbackURLs?: string[] | undefined;
  LogoutURLs?: string[] | undefined;
  DefaultRedirectURI?: string | undefined;
  AllowedOAuthFlows?: OAuthFlowType[] | undefined;
  AllowedOAuthScopes?: string[] | undefined;
  AllowedOAuthFlowsUserPoolClient?: boolean | undefined;
  AnalyticsConfiguration?: AnalyticsConfigurationType | undefined;
  PreventUserExistenceErrors?: PreventUserExistenceErrorTypes | undefined;
  EnableTokenRevocation?: boolean | undefined;
  EnablePropagateAdditionalUserContextData?: boolean | undefined;
  AuthSessionValidity?: number | undefined;
  RefreshTokenRotation?: RefreshTokenRotationType | undefined;
}
export interface UpdateUserPoolClientResponse {
  UserPoolClient?: UserPoolClientType | undefined;
}
export interface UpdateUserPoolDomainRequest {
  Domain: string | undefined;
  UserPoolId: string | undefined;
  ManagedLoginVersion?: number | undefined;
  CustomDomainConfig?: CustomDomainConfigType | undefined;
}
export interface UpdateUserPoolDomainResponse {
  ManagedLoginVersion?: number | undefined;
  CloudFrontDomain?: string | undefined;
}
export declare class EnableSoftwareTokenMFAException extends __BaseException {
  readonly name: "EnableSoftwareTokenMFAException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      EnableSoftwareTokenMFAException,
      __BaseException
    >
  );
}
export interface VerifySoftwareTokenRequest {
  AccessToken?: string | undefined;
  Session?: string | undefined;
  UserCode: string | undefined;
  FriendlyDeviceName?: string | undefined;
}
export declare const VerifySoftwareTokenResponseType: {
  readonly ERROR: "ERROR";
  readonly SUCCESS: "SUCCESS";
};
export type VerifySoftwareTokenResponseType =
  (typeof VerifySoftwareTokenResponseType)[keyof typeof VerifySoftwareTokenResponseType];
export interface VerifySoftwareTokenResponse {
  Status?: VerifySoftwareTokenResponseType | undefined;
  Session?: string | undefined;
}
export interface VerifyUserAttributeRequest {
  AccessToken: string | undefined;
  AttributeName: string | undefined;
  Code: string | undefined;
}
export interface VerifyUserAttributeResponse {}
export declare const GetUserAuthFactorsRequestFilterSensitiveLog: (
  obj: GetUserAuthFactorsRequest
) => any;
export declare const GetUserAuthFactorsResponseFilterSensitiveLog: (
  obj: GetUserAuthFactorsResponse
) => any;
export declare const GlobalSignOutRequestFilterSensitiveLog: (
  obj: GlobalSignOutRequest
) => any;
export declare const InitiateAuthRequestFilterSensitiveLog: (
  obj: InitiateAuthRequest
) => any;
export declare const InitiateAuthResponseFilterSensitiveLog: (
  obj: InitiateAuthResponse
) => any;
export declare const ListDevicesRequestFilterSensitiveLog: (
  obj: ListDevicesRequest
) => any;
export declare const ListDevicesResponseFilterSensitiveLog: (
  obj: ListDevicesResponse
) => any;
export declare const UserPoolClientDescriptionFilterSensitiveLog: (
  obj: UserPoolClientDescription
) => any;
export declare const ListUserPoolClientsResponseFilterSensitiveLog: (
  obj: ListUserPoolClientsResponse
) => any;
export declare const ListUsersResponseFilterSensitiveLog: (
  obj: ListUsersResponse
) => any;
export declare const ListUsersInGroupResponseFilterSensitiveLog: (
  obj: ListUsersInGroupResponse
) => any;
export declare const ListWebAuthnCredentialsRequestFilterSensitiveLog: (
  obj: ListWebAuthnCredentialsRequest
) => any;
export declare const ResendConfirmationCodeRequestFilterSensitiveLog: (
  obj: ResendConfirmationCodeRequest
) => any;
export declare const RespondToAuthChallengeRequestFilterSensitiveLog: (
  obj: RespondToAuthChallengeRequest
) => any;
export declare const RespondToAuthChallengeResponseFilterSensitiveLog: (
  obj: RespondToAuthChallengeResponse
) => any;
export declare const RevokeTokenRequestFilterSensitiveLog: (
  obj: RevokeTokenRequest
) => any;
export declare const SetRiskConfigurationRequestFilterSensitiveLog: (
  obj: SetRiskConfigurationRequest
) => any;
export declare const SetRiskConfigurationResponseFilterSensitiveLog: (
  obj: SetRiskConfigurationResponse
) => any;
export declare const SetUICustomizationRequestFilterSensitiveLog: (
  obj: SetUICustomizationRequest
) => any;
export declare const SetUICustomizationResponseFilterSensitiveLog: (
  obj: SetUICustomizationResponse
) => any;
export declare const SetUserMFAPreferenceRequestFilterSensitiveLog: (
  obj: SetUserMFAPreferenceRequest
) => any;
export declare const SetUserSettingsRequestFilterSensitiveLog: (
  obj: SetUserSettingsRequest
) => any;
export declare const SignUpRequestFilterSensitiveLog: (
  obj: SignUpRequest
) => any;
export declare const SignUpResponseFilterSensitiveLog: (
  obj: SignUpResponse
) => any;
export declare const StartWebAuthnRegistrationRequestFilterSensitiveLog: (
  obj: StartWebAuthnRegistrationRequest
) => any;
export declare const UpdateAuthEventFeedbackRequestFilterSensitiveLog: (
  obj: UpdateAuthEventFeedbackRequest
) => any;
export declare const UpdateDeviceStatusRequestFilterSensitiveLog: (
  obj: UpdateDeviceStatusRequest
) => any;
export declare const UpdateUserAttributesRequestFilterSensitiveLog: (
  obj: UpdateUserAttributesRequest
) => any;
export declare const UpdateUserPoolClientRequestFilterSensitiveLog: (
  obj: UpdateUserPoolClientRequest
) => any;
export declare const UpdateUserPoolClientResponseFilterSensitiveLog: (
  obj: UpdateUserPoolClientResponse
) => any;
export declare const VerifySoftwareTokenRequestFilterSensitiveLog: (
  obj: VerifySoftwareTokenRequest
) => any;
export declare const VerifySoftwareTokenResponseFilterSensitiveLog: (
  obj: VerifySoftwareTokenResponse
) => any;
export declare const VerifyUserAttributeRequestFilterSensitiveLog: (
  obj: VerifyUserAttributeRequest
) => any;
