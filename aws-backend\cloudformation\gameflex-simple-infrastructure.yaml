AWSTemplateFormatVersion: "2010-09-09"
Description: "GameFlex Simple AWS Infrastructure - LocalStack Free Services Only"

Parameters:
  Environment:
    Type: String
    Default: development
    AllowedValues:
      - development
      - staging
      - production
    Description: Environment name

  ProjectName:
    Type: String
    Default: gameflex
    Description: Project name for resource naming

Resources:
  # S3 Buckets (Free in LocalStack)
  MediaBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "${ProjectName}-media-${Environment}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false

  AvatarsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "${ProjectName}-avatars-${Environment}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false

  TempBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "${ProjectName}-temp-${Environment}"

  # IAM Role for Lambda Functions (Free in LocalStack)
  LambdaExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub "${ProjectName}-lambda-execution-role-${Environment}"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: S3Access
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:ListBucket
                Resource:
                  - !GetAtt MediaBucket.Arn
                  - !Sub "${MediaBucket.Arn}/*"
                  - !GetAtt AvatarsBucket.Arn
                  - !Sub "${AvatarsBucket.Arn}/*"
                  - !GetAtt TempBucket.Arn
                  - !Sub "${TempBucket.Arn}/*"

  # Lambda Functions (Free in LocalStack)
  AuthLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub "${ProjectName}-auth-${Environment}"
      Runtime: nodejs18.x
      Handler: dist/handler.handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 30
      MemorySize: 256
      Code:
        ZipFile: |
          exports.handler = async (event, context) => {
              return {
                  statusCode: 200,
                  body: JSON.stringify({message: "Auth function placeholder"})
              };
          };
      Environment:
        Variables:
          DYNAMODB_ENDPOINT_URL: http://localhost:4566
          DYNAMODB_REGION: us-east-1
          DYNAMODB_TABLE_USERS: Users
          DYNAMODB_TABLE_USER_PROFILES: UserProfiles
          DYNAMODB_TABLE_CHANNELS: Channels
          DYNAMODB_TABLE_CHANNEL_MEMBERS: ChannelMembers
          DYNAMODB_TABLE_MEDIA: Media
          DYNAMODB_TABLE_POSTS: Posts
          DYNAMODB_TABLE_COMMENTS: Comments
          DYNAMODB_TABLE_LIKES: Likes
          DYNAMODB_TABLE_FOLLOWS: Follows
          DYNAMODB_TABLE_NOTIFICATIONS: Notifications
          AWS_ENDPOINT_URL: http://localhost:45660

  PostsLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub "${ProjectName}-posts-${Environment}"
      Runtime: nodejs18.x
      Handler: dist/handler.handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 30
      MemorySize: 256
      Code:
        ZipFile: |
          exports.handler = async (event, context) => {
              return {
                  statusCode: 200,
                  body: JSON.stringify({message: "Posts function placeholder"})
              };
          };
      Environment:
        Variables:
          DYNAMODB_ENDPOINT_URL: http://localhost:4566
          DYNAMODB_REGION: us-east-1
          DYNAMODB_TABLE_USERS: Users
          DYNAMODB_TABLE_USER_PROFILES: UserProfiles
          DYNAMODB_TABLE_CHANNELS: Channels
          DYNAMODB_TABLE_CHANNEL_MEMBERS: ChannelMembers
          DYNAMODB_TABLE_MEDIA: Media
          DYNAMODB_TABLE_POSTS: Posts
          DYNAMODB_TABLE_COMMENTS: Comments
          DYNAMODB_TABLE_LIKES: Likes
          DYNAMODB_TABLE_FOLLOWS: Follows
          DYNAMODB_TABLE_NOTIFICATIONS: Notifications
          S3_BUCKET_MEDIA: !Ref MediaBucket
          AWS_ENDPOINT_URL: http://localhost:45660

  MediaLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub "${ProjectName}-media-${Environment}"
      Runtime: nodejs18.x
      Handler: dist/handler.handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 60
      MemorySize: 512
      Code:
        ZipFile: |
          exports.handler = async (event, context) => {
              return {
                  statusCode: 200,
                  body: JSON.stringify({message: "Media function placeholder"})
              };
          };
      Environment:
        Variables:
          DYNAMODB_ENDPOINT_URL: http://localhost:4566
          DYNAMODB_REGION: us-east-1
          DYNAMODB_TABLE_USERS: Users
          DYNAMODB_TABLE_USER_PROFILES: UserProfiles
          DYNAMODB_TABLE_CHANNELS: Channels
          DYNAMODB_TABLE_CHANNEL_MEMBERS: ChannelMembers
          DYNAMODB_TABLE_MEDIA: Media
          DYNAMODB_TABLE_POSTS: Posts
          DYNAMODB_TABLE_COMMENTS: Comments
          DYNAMODB_TABLE_LIKES: Likes
          DYNAMODB_TABLE_FOLLOWS: Follows
          DYNAMODB_TABLE_NOTIFICATIONS: Notifications
          S3_BUCKET_MEDIA: !Ref MediaBucket
          S3_BUCKET_AVATARS: !Ref AvatarsBucket
          S3_BUCKET_TEMP: !Ref TempBucket
          MAX_FILE_SIZE: "52428800"
          AWS_ENDPOINT_URL: http://localhost:45660

  UsersLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub "${ProjectName}-users-${Environment}"
      Runtime: nodejs18.x
      Handler: dist/handler.handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 60
      MemorySize: 512
      Code:
        ZipFile: |
          exports.handler = async (event, context) => {
              return {
                  statusCode: 200,
                  body: JSON.stringify({message: "Users function placeholder"})
              };
          };
      Environment:
        Variables:
          DYNAMODB_ENDPOINT_URL: http://localhost:4566
          DYNAMODB_REGION: us-east-1
          DYNAMODB_TABLE_USERS: Users
          DYNAMODB_TABLE_USER_PROFILES: UserProfiles
          DYNAMODB_TABLE_CHANNELS: Channels
          DYNAMODB_TABLE_CHANNEL_MEMBERS: ChannelMembers
          DYNAMODB_TABLE_MEDIA: Media
          DYNAMODB_TABLE_POSTS: Posts
          DYNAMODB_TABLE_COMMENTS: Comments
          DYNAMODB_TABLE_LIKES: Likes
          DYNAMODB_TABLE_FOLLOWS: Follows
          DYNAMODB_TABLE_NOTIFICATIONS: Notifications
          S3_BUCKET_MEDIA: !Ref MediaBucket
          S3_BUCKET_AVATARS: !Ref AvatarsBucket
          S3_BUCKET_TEMP: !Ref TempBucket
          MAX_FILE_SIZE: "52428800"
          AWS_ENDPOINT_URL: http://localhost:45660

Outputs:
  MediaBucketName:
    Description: S3 Media Bucket Name
    Value: !Ref MediaBucket
    Export:
      Name: !Sub "${ProjectName}-media-bucket-${Environment}"

  AvatarsBucketName:
    Description: S3 Avatars Bucket Name
    Value: !Ref AvatarsBucket
    Export:
      Name: !Sub "${ProjectName}-avatars-bucket-${Environment}"

  TempBucketName:
    Description: S3 Temp Bucket Name
    Value: !Ref TempBucket
    Export:
      Name: !Sub "${ProjectName}-temp-bucket-${Environment}"

  AuthLambdaArn:
    Description: Auth Lambda Function ARN
    Value: !GetAtt AuthLambda.Arn
    Export:
      Name: !Sub "${ProjectName}-auth-lambda-arn-${Environment}"

  PostsLambdaArn:
    Description: Posts Lambda Function ARN
    Value: !GetAtt PostsLambda.Arn
    Export:
      Name: !Sub "${ProjectName}-posts-lambda-arn-${Environment}"

  MediaLambdaArn:
    Description: Media Lambda Function ARN
    Value: !GetAtt MediaLambda.Arn
    Export:
      Name: !Sub "${ProjectName}-media-lambda-arn-${Environment}"

  UsersLambdaArn:
    Description: Users Lambda Function ARN
    Value: !GetAtt UsersLambda.Arn
    Export:
      Name: !Sub "${ProjectName}-users-lambda-arn-${Environment}"
