/**
 * The blob Smithy type, in JS as Uint8Array and other representations
 * such as Buffer, string, or Readable(Stream) depending on circumstances.
 * @public
 */
export type BlobSchema = 21;
/**
 * @public
 */
export type StreamingBlobSchema = 42;
/**
 * @public
 */
export type BooleanSchema = 2;
/**
 * Includes string and enum Smithy types.
 * @public
 */
export type StringSchema = 0;
/**
 * Includes all numeric Smithy types except bigInteger and bigDecimal.
 * byte, short, integer, long, float, double, intEnum.
 *
 * @public
 */
export type NumericSchema = 1;
/**
 * @public
 */
export type BigIntegerSchema = 17;
/**
 * @public
 */
export type BigDecimalSchema = 19;
/**
 * @public
 */
export type DocumentSchema = 15;
/**
 * Smithy type timestamp, in JS as native Date object.
 * @public
 */
export type TimestampDefaultSchema = 4;
/**
 * @public
 */
export type TimestampDateTimeSchema = 5;
/**
 * @public
 */
export type TimestampHttpDateSchema = 6;
/**
 * @public
 */
export type TimestampEpochSecondsSchema = 7;
/**
 * Additional bit indicating the type is a list.
 * @public
 */
export type ListSchemaModifier = 64;
/**
 * Additional bit indicating the type is a map.
 * @public
 */
export type MapSchemaModifier = 128;
