/**
 * @internal
 *
 * Given an input string, splits based on the delimiter after a given
 * number of delimiters has been encountered.
 *
 * @param value - The input string to split.
 * @param delimiter - The delimiter to split on.
 * @param numDelimiters - The number of delimiters to have encountered to split.
 */
export declare function splitEvery(value: string, delimiter: string, numDelimiters: number): Array<string>;
