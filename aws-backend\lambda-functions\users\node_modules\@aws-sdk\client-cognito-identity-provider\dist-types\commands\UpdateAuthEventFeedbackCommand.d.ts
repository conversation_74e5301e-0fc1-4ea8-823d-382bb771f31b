import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { CognitoIdentityProviderClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../CognitoIdentityProviderClient";
import { UpdateAuthEventFeedbackRequest, UpdateAuthEventFeedbackResponse } from "../models/models_1";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link UpdateAuthEventFeedbackCommand}.
 */
export interface UpdateAuthEventFeedbackCommandInput extends UpdateAuthEventFeedbackRequest {
}
/**
 * @public
 *
 * The output of {@link UpdateAuthEventFeedbackCommand}.
 */
export interface UpdateAuthEventFeedbackCommandOutput extends UpdateAuthEventFeedbackResponse, __MetadataBearer {
}
declare const UpdateAuthEventFeedbackCommand_base: {
    new (input: UpdateAuthEventFeedbackCommandInput): import("@smithy/smithy-client").CommandImpl<UpdateAuthEventFeedbackCommandInput, UpdateAuthEventFeedbackCommandOutput, CognitoIdentityProviderClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: UpdateAuthEventFeedbackCommandInput): import("@smithy/smithy-client").CommandImpl<UpdateAuthEventFeedbackCommandInput, UpdateAuthEventFeedbackCommandOutput, CognitoIdentityProviderClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Provides the feedback for an authentication event generated by threat protection
 *             features. The user's response indicates that you think that the event either was from a
 *             valid user or was an unwanted authentication attempt. This feedback improves the risk
 *             evaluation decision for the user pool as part of Amazon Cognito threat protection.
 *             To activate this setting, your user pool must be on the <a href="https://docs.aws.amazon.com/cognito/latest/developerguide/feature-plans-features-plus.html">
 *                      Plus tier</a>.</p>
 *          <p>This operation requires a <code>FeedbackToken</code> that Amazon Cognito generates and adds to
 *             notification emails when users have potentially suspicious authentication events. Users
 *             invoke this operation when they select the link that corresponds to
 *                 <code>\{one-click-link-valid\}</code> or <code>\{one-click-link-invalid\}</code> in your
 *             notification template. Because <code>FeedbackToken</code> is a required parameter, you
 *             can' make requests to <code>UpdateAuthEventFeedback</code> without the contents of
 *             the notification email message.</p>
 *          <note>
 *             <p>Amazon Cognito doesn't evaluate Identity and Access Management (IAM) policies in requests for this API operation. For
 *     this operation, you can't use IAM credentials to authorize requests, and you can't
 *     grant IAM permissions in policies. For more information about authorization models in
 *     Amazon Cognito, see <a href="https://docs.aws.amazon.com/cognito/latest/developerguide/user-pools-API-operations.html">Using the Amazon Cognito user pools API and user pool endpoints</a>.</p>
 *          </note>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { CognitoIdentityProviderClient, UpdateAuthEventFeedbackCommand } from "@aws-sdk/client-cognito-identity-provider"; // ES Modules import
 * // const { CognitoIdentityProviderClient, UpdateAuthEventFeedbackCommand } = require("@aws-sdk/client-cognito-identity-provider"); // CommonJS import
 * const client = new CognitoIdentityProviderClient(config);
 * const input = { // UpdateAuthEventFeedbackRequest
 *   UserPoolId: "STRING_VALUE", // required
 *   Username: "STRING_VALUE", // required
 *   EventId: "STRING_VALUE", // required
 *   FeedbackToken: "STRING_VALUE", // required
 *   FeedbackValue: "Valid" || "Invalid", // required
 * };
 * const command = new UpdateAuthEventFeedbackCommand(input);
 * const response = await client.send(command);
 * // {};
 *
 * ```
 *
 * @param UpdateAuthEventFeedbackCommandInput - {@link UpdateAuthEventFeedbackCommandInput}
 * @returns {@link UpdateAuthEventFeedbackCommandOutput}
 * @see {@link UpdateAuthEventFeedbackCommandInput} for command's `input` shape.
 * @see {@link UpdateAuthEventFeedbackCommandOutput} for command's `response` shape.
 * @see {@link CognitoIdentityProviderClientResolvedConfig | config} for CognitoIdentityProviderClient's `config` shape.
 *
 * @throws {@link InternalErrorException} (server fault)
 *  <p>This exception is thrown when Amazon Cognito encounters an internal error.</p>
 *
 * @throws {@link InvalidParameterException} (client fault)
 *  <p>This exception is thrown when the Amazon Cognito service encounters an invalid
 *             parameter.</p>
 *
 * @throws {@link NotAuthorizedException} (client fault)
 *  <p>This exception is thrown when a user isn't authorized.</p>
 *
 * @throws {@link ResourceNotFoundException} (client fault)
 *  <p>This exception is thrown when the Amazon Cognito service can't find the requested
 *             resource.</p>
 *
 * @throws {@link TooManyRequestsException} (client fault)
 *  <p>This exception is thrown when the user has made too many requests for a given
 *             operation.</p>
 *
 * @throws {@link UserNotFoundException} (client fault)
 *  <p>This exception is thrown when a user isn't found.</p>
 *
 * @throws {@link UserPoolAddOnNotEnabledException} (client fault)
 *  <p>This exception is thrown when user pool add-ons aren't enabled.</p>
 *
 * @throws {@link CognitoIdentityProviderServiceException}
 * <p>Base exception class for all service exceptions from CognitoIdentityProvider service.</p>
 *
 *
 * @public
 */
export declare class UpdateAuthEventFeedbackCommand extends UpdateAuthEventFeedbackCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: UpdateAuthEventFeedbackRequest;
            output: {};
        };
        sdk: {
            input: UpdateAuthEventFeedbackCommandInput;
            output: UpdateAuthEventFeedbackCommandOutput;
        };
    };
}
