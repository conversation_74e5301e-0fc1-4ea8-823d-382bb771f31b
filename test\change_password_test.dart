import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/providers/auth_provider.dart';
import 'package:flutter/foundation.dart';
import 'test_helpers.dart';

void main() {
  group('Change Password Tests (AWS Backend)', () {
    late AuthProvider authProvider;

    setUpAll(() async {
      // Initialize Flutter binding for tests
      TestHelpers.setupTestEnvironment();
    });

    setUp(() {
      authProvider = AuthProvider();
    });

    tearDown(() {
      authProvider.dispose();
    });

    test(
      'should successfully change password for authenticated user',
      () async {
        // First, sign up a test user
        const testEmail = '<EMAIL>';
        const originalPassword = 'testpassword123';
        const newPassword = 'newpassword456';

        try {
          // Sign up
          final signUpSuccess = await authProvider.signUp(
            email: testEmail,
            password: originalPassword,
          );

          if (kDebugMode) {
            print('Sign up success: $signUpSuccess');
            print('Auth status: ${authProvider.status}');
            print('User: ${authProvider.user?.email}');
          }

          if (!signUpSuccess || !authProvider.isAuthenticated) {
            print(
              '⚠️  Skipping test - sign up failed or user not authenticated',
            );
            print('💡 Make sure AWS backend is running: aws-backend/start.ps1');
            return; // Skip test if sign up fails
          }

          // Wait a moment for the authentication to settle
          await Future.delayed(const Duration(seconds: 1));

          // Change password
          final changeSuccess = await authProvider.changePassword(newPassword);

          if (kDebugMode) {
            print('Change password success: $changeSuccess');
            if (authProvider.errorMessage != null) {
              print('Error message: ${authProvider.errorMessage}');
            }
          }

          expect(changeSuccess, true);

          // Sign out
          await authProvider.signOut();
          expect(authProvider.isAuthenticated, false);

          // Try to sign in with old password (should fail)
          final oldPasswordSignIn = await authProvider.signIn(
            email: testEmail,
            password: originalPassword,
          );

          if (kDebugMode) {
            print('Old password sign in: $oldPasswordSignIn');
          }

          expect(oldPasswordSignIn, false);

          // Try to sign in with new password (should succeed)
          final newPasswordSignIn = await authProvider.signIn(
            email: testEmail,
            password: newPassword,
          );

          if (kDebugMode) {
            print('New password sign in: $newPasswordSignIn');
          }

          expect(newPasswordSignIn, true);
          expect(authProvider.isAuthenticated, true);
        } catch (e) {
          if (kDebugMode) {
            print('Test error: $e');
          }
          fail('Test failed with error: $e');
        }
      },
      timeout: const Timeout(Duration(minutes: 2)),
    );

    test('should fail to change password when not authenticated', () async {
      // Ensure user is not authenticated
      expect(authProvider.isAuthenticated, false);

      // Try to change password
      const newPassword = 'newpassword123';
      final changeSuccess = await authProvider.changePassword(newPassword);

      if (kDebugMode) {
        print('Change password when not authenticated: $changeSuccess');
        if (authProvider.errorMessage != null) {
          print('Error message: ${authProvider.errorMessage}');
        }
      }

      expect(changeSuccess, false);
      expect(authProvider.errorMessage, isNotNull);
    });

    test(
      'should validate password requirements',
      () async {
        // This test would be for client-side validation
        // The actual validation happens in the form, but we can test the auth service directly

        const testEmail = '<EMAIL>';
        const originalPassword = 'testpassword123';

        try {
          // Sign up first
          final signUpSuccess = await authProvider.signUp(
            email: testEmail,
            password: originalPassword,
          );

          if (!signUpSuccess || !authProvider.isAuthenticated) {
            print(
              '⚠️  Skipping validation test - sign up failed or user not authenticated',
            );
            print('💡 Make sure AWS backend is running: aws-backend/start.ps1');
            return; // Skip test if sign up fails
          }

          // Wait a moment for authentication to settle
          await Future.delayed(const Duration(seconds: 1));

          // Try to change to a very short password (should fail)
          const shortPassword = '123';
          final changeSuccess = await authProvider.changePassword(
            shortPassword,
          );

          if (kDebugMode) {
            print('Short password change success: $changeSuccess');
            if (authProvider.errorMessage != null) {
              print('Error message: ${authProvider.errorMessage}');
            }
          }

          // This might succeed at the service level but should be caught by form validation
          // The actual validation should happen in the UI form
        } catch (e) {
          if (kDebugMode) {
            print('Validation test error: $e');
          }
          // Some errors are expected for invalid passwords
        }
      },
      timeout: const Timeout(Duration(minutes: 2)),
    );
  });
}
