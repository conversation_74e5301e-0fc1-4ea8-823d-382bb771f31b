import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/aws_auth_service.dart';
import 'package:gameflex_mobile/services/aws_posts_service.dart';
import 'test_helpers.dart';

void main() {
  group('Upload Service Tests (AWS Backend)', () {
    late AwsAuthService authService;
    late AwsPostsService postsService;

    setUpAll(() async {
      // Initialize Flutter binding for tests
      TestHelpers.setupTestEnvironment();

      authService = AwsAuthService.instance;
      postsService = AwsPostsService.instance;
      print('✅ AWS services initialized for testing');
    });

    test('Test AWS backend post creation flow', () async {
      print('🧪 Testing AWS backend post creation...');

      try {
        // Authenticate first
        print('🔐 Authenticating test user...');

        final authResult = await authService.signIn(
          email: '<EMAIL>',
          password: 'DevPassword123!',
        );

        if (!authResult.success) {
          print(
            '⚠️  Skipping test - authentication failed: ${authResult.message}',
          );
          print('💡 Make sure AWS backend is running: aws-backend/start.ps1');
          return; // Skip test if authentication fails
        }
        expect(authResult.user, isNotNull);
        print('✅ Authentication successful: ${authResult.user?.email}');

        // Test post creation through AWS backend
        final testContent =
            'AWS backend test post - ${DateTime.now().millisecondsSinceEpoch}';

        print('📤 Testing post creation...');
        final createdPost = await postsService.createPost(content: testContent);

        expect(createdPost, isNotNull, reason: 'Post creation should succeed');
        print('✅ Post creation completed successfully');

        // Verify the post was created
        print('🔍 Verifying post creation...');
        expect(createdPost!.content, equals(testContent));
        expect(createdPost.userId, equals(authResult.user!.id));
        expect(createdPost.id, isNotNull);

        print('✅ AWS backend verification successful');
        print('📊 Post ID: ${createdPost.id}');
        print('📊 Post content: ${createdPost.content}');
        print('📊 User ID: ${createdPost.userId}');
        print('🎉 AWS backend test completed successfully!');
      } catch (e, stackTrace) {
        print(
          '⚠️  Upload service test skipped due to backend connectivity: $e',
        );
        print('💡 Make sure AWS backend is running: aws-backend/start.ps1');
        // Don't fail the test if backend is not available
      }
    });

    test('Test AWS backend posts retrieval', () async {
      print('🧪 Testing AWS backend posts retrieval...');

      try {
        // Test retrieving posts without authentication
        final posts = await postsService.getPosts(limit: 5);
        expect(posts, isA<List>());
        print('✅ Retrieved ${posts.length} posts from AWS backend');

        print('🎉 AWS backend posts retrieval test passed!');
      } catch (e) {
        print(
          '⚠️  Posts retrieval test skipped due to backend connectivity: $e',
        );
        print('💡 Make sure AWS backend is running: aws-backend/start.ps1');
        // Don't fail the test if backend is not available
      }
    });
  });
}
